#!/usr/bin/env python3
"""
n8n Template Scraper
Scrapes all workflow templates from n8n.io to create a comprehensive dataset for LLM RAG
"""

import requests
import json
import time
import re
from urllib.parse import urljoin, urlparse
from bs4 import BeautifulSoup
import os
from datetime import datetime
import logging

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class N8nTemplateScraper:
    def __init__(self):
        self.base_url = "https://n8n.io"
        self.workflows_url = "https://n8n.io/workflows/"
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        })
        self.scraped_workflows = []
        self.failed_urls = []
        
    def get_workflow_list_pages(self):
        """Get all workflow list pages to find individual workflow URLs"""
        workflow_urls = set()
        page = 1
        
        while True:
            try:
                if page == 1:
                    url = self.workflows_url
                else:
                    url = f"{self.workflows_url}?page={page}"
                
                logger.info(f"Fetching workflow list page {page}: {url}")
                response = self.session.get(url, timeout=30)
                response.raise_for_status()
                
                soup = BeautifulSoup(response.content, 'html.parser')
                
                # Find workflow links
                workflow_links = soup.find_all('a', href=re.compile(r'/workflows/\d+'))
                
                if not workflow_links:
                    logger.info(f"No more workflows found on page {page}")
                    break
                
                for link in workflow_links:
                    href = link.get('href')
                    if href:
                        full_url = urljoin(self.base_url, href)
                        workflow_urls.add(full_url)
                
                logger.info(f"Found {len(workflow_links)} workflows on page {page}")
                page += 1
                time.sleep(1)  # Be respectful
                
            except Exception as e:
                logger.error(f"Error fetching page {page}: {e}")
                break
        
        logger.info(f"Total unique workflow URLs found: {len(workflow_urls)}")
        return list(workflow_urls)
    
    def extract_workflow_json(self, url):
        """Extract workflow JSON from a template page"""
        try:
            logger.info(f"Scraping workflow: {url}")
            response = self.session.get(url, timeout=30)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Extract metadata
            title = soup.find('h1')
            title_text = title.get_text().strip() if title else "Unknown Workflow"
            
            # Extract description
            description = ""
            desc_section = soup.find('div', class_='workflow-description') or soup.find('section', class_='description')
            if desc_section:
                description = desc_section.get_text().strip()
            
            # Extract categories
            categories = []
            category_links = soup.find_all('a', href=re.compile(r'/workflows/categories/'))
            for cat_link in category_links:
                categories.append(cat_link.get_text().strip())
            
            # Look for workflow JSON in various places
            workflow_json = None
            
            # Method 1: Look for n8n-demo component with workflow attribute
            n8n_demo = soup.find('n8n-demo')
            if n8n_demo and n8n_demo.get('workflow'):
                try:
                    workflow_json = json.loads(n8n_demo.get('workflow'))
                except json.JSONDecodeError:
                    pass
            
            # Method 2: Look for JSON in script tags
            if not workflow_json:
                script_tags = soup.find_all('script')
                for script in script_tags:
                    if script.string:
                        # Look for workflow JSON patterns
                        json_match = re.search(r'workflow["\']?\s*:\s*({.*?})\s*[,}]', script.string, re.DOTALL)
                        if json_match:
                            try:
                                workflow_json = json.loads(json_match.group(1))
                                break
                            except json.JSONDecodeError:
                                continue
            
            # Method 3: Look for data attributes
            if not workflow_json:
                workflow_data = soup.find(attrs={'data-workflow': True})
                if workflow_data:
                    try:
                        workflow_json = json.loads(workflow_data.get('data-workflow'))
                    except json.JSONDecodeError:
                        pass
            
            # Method 4: Try to find download/export links
            if not workflow_json:
                export_links = soup.find_all('a', href=re.compile(r'\.json$|export|download'))
                for link in export_links:
                    try:
                        json_url = urljoin(url, link.get('href'))
                        json_response = self.session.get(json_url, timeout=15)
                        if json_response.status_code == 200:
                            workflow_json = json_response.json()
                            break
                    except:
                        continue
            
            if workflow_json:
                # Extract additional metadata
                node_count = len(workflow_json.get('nodes', []))
                connection_count = len(workflow_json.get('connections', {}))
                
                # Get node types
                node_types = []
                for node in workflow_json.get('nodes', []):
                    node_type = node.get('type', '')
                    if node_type and node_type not in node_types:
                        node_types.append(node_type)
                
                workflow_data = {
                    'url': url,
                    'title': title_text,
                    'description': description,
                    'categories': categories,
                    'node_count': node_count,
                    'connection_count': connection_count,
                    'node_types': node_types,
                    'workflow_json': workflow_json,
                    'scraped_at': datetime.now().isoformat()
                }
                
                logger.info(f"Successfully extracted workflow: {title_text} ({node_count} nodes)")
                return workflow_data
            else:
                logger.warning(f"No workflow JSON found for: {url}")
                return None
                
        except Exception as e:
            logger.error(f"Error scraping {url}: {e}")
            self.failed_urls.append(url)
            return None
    
    def scrape_all_workflows(self):
        """Main method to scrape all workflows"""
        logger.info("Starting n8n template scraping...")
        
        # Get all workflow URLs
        workflow_urls = self.get_workflow_list_pages()
        
        # Scrape each workflow
        for i, url in enumerate(workflow_urls, 1):
            logger.info(f"Processing {i}/{len(workflow_urls)}: {url}")
            
            workflow_data = self.extract_workflow_json(url)
            if workflow_data:
                self.scraped_workflows.append(workflow_data)
            
            # Be respectful - add delay
            time.sleep(2)
            
            # Save progress every 50 workflows
            if i % 50 == 0:
                self.save_progress()
        
        logger.info(f"Scraping completed. Successfully scraped {len(self.scraped_workflows)} workflows")
        logger.info(f"Failed URLs: {len(self.failed_urls)}")
        
        return self.scraped_workflows
    
    def save_progress(self):
        """Save current progress to file"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"n8n_workflows_progress_{timestamp}.json"
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump({
                'scraped_workflows': self.scraped_workflows,
                'failed_urls': self.failed_urls,
                'total_scraped': len(self.scraped_workflows),
                'total_failed': len(self.failed_urls)
            }, f, indent=2, ensure_ascii=False)
        
        logger.info(f"Progress saved to {filename}")
    
    def save_final_dataset(self):
        """Save the final comprehensive dataset"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Save complete dataset
        complete_filename = f"n8n_workflows_complete_{timestamp}.json"
        with open(complete_filename, 'w', encoding='utf-8') as f:
            json.dump({
                'metadata': {
                    'total_workflows': len(self.scraped_workflows),
                    'failed_urls': len(self.failed_urls),
                    'scraped_at': datetime.now().isoformat(),
                    'source': 'n8n.io/workflows'
                },
                'workflows': self.scraped_workflows,
                'failed_urls': self.failed_urls
            }, f, indent=2, ensure_ascii=False)
        
        # Save RAG-optimized dataset (workflows only)
        rag_filename = f"n8n_workflows_rag_{timestamp}.json"
        rag_data = []
        
        for workflow in self.scraped_workflows:
            rag_entry = {
                'title': workflow['title'],
                'description': workflow['description'],
                'categories': workflow['categories'],
                'node_types': workflow['node_types'],
                'node_count': workflow['node_count'],
                'workflow_json': workflow['workflow_json'],
                'use_case': f"{workflow['title']} - {workflow['description']}"
            }
            rag_data.append(rag_entry)
        
        with open(rag_filename, 'w', encoding='utf-8') as f:
            json.dump(rag_data, f, indent=2, ensure_ascii=False)
        
        logger.info(f"Complete dataset saved to {complete_filename}")
        logger.info(f"RAG dataset saved to {rag_filename}")
        
        return complete_filename, rag_filename

if __name__ == "__main__":
    scraper = N8nTemplateScraper()
    
    try:
        # Create output directory
        os.makedirs("scraped_data", exist_ok=True)
        os.chdir("scraped_data")
        
        # Scrape all workflows
        workflows = scraper.scrape_all_workflows()
        
        # Save final datasets
        complete_file, rag_file = scraper.save_final_dataset()
        
        print(f"\n🎉 Scraping completed!")
        print(f"📊 Total workflows scraped: {len(workflows)}")
        print(f"❌ Failed URLs: {len(scraper.failed_urls)}")
        print(f"💾 Complete dataset: {complete_file}")
        print(f"🤖 RAG dataset: {rag_file}")
        
    except KeyboardInterrupt:
        logger.info("Scraping interrupted by user")
        scraper.save_progress()
    except Exception as e:
        logger.error(f"Scraping failed: {e}")
        scraper.save_progress()
