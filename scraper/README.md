# n8n Workflow Scraper Suite

A comprehensive toolkit for scraping n8n workflow templates from multiple sources to create datasets for LLM training and RAG systems.

## 🎯 Purpose

This scraper suite collects n8n workflow templates from various sources to create comprehensive datasets for:
- **LLM Fine-tuning**: Training models to generate n8n workflows
- **RAG Systems**: Retrieval-augmented generation for workflow assistance
- **Research**: Analysis of automation patterns and best practices
- **Template Libraries**: Building comprehensive workflow collections

## 🔧 Components

### 1. `n8n_template_scraper.py`
Scrapes workflow templates from the official n8n.io/workflows pages.
- Extracts workflow JSON, metadata, and descriptions
- Handles pagination and rate limiting
- Saves progress incrementally

### 2. `n8n_api_scraper.py`
Enhanced scraper that uses multiple sources:
- n8n API endpoints (if available)
- GitHub repositories with n8n workflows
- n8n community forum workflows
- Async processing for better performance

### 3. `workflow_analyzer.py`
Analyzes scraped workflows to create optimized datasets:
- Pattern analysis (node types, combinations, complexity)
- Training example generation
- RAG knowledge base creation
- Statistical analysis and reporting

### 4. `run_complete_scraping.py`
Orchestrator that runs all scraping methods:
- Combines all sources
- Deduplicates workflows
- Creates multiple dataset formats
- Generates comprehensive reports

## 🚀 Quick Start

### Installation

```bash
# Install dependencies
pip install -r requirements.txt

# Make scripts executable
chmod +x *.py
```

### Basic Usage

```bash
# Run complete scraping (recommended)
python run_complete_scraping.py

# Or run individual scrapers
python n8n_template_scraper.py
python n8n_api_scraper.py
```

### Advanced Usage

```python
# Custom scraping with specific parameters
from n8n_template_scraper import N8nTemplateScraper

scraper = N8nTemplateScraper()
workflows = scraper.scrape_all_workflows()
scraper.save_final_dataset()
```

## 📊 Output Datasets

The scraper creates multiple dataset formats optimized for different use cases:

### 1. Complete Dataset (`complete_dataset_*.json`)
```json
{
  "metadata": {
    "total_workflows": 1500,
    "sources": ["n8n_templates", "github", "community"],
    "created_at": "2024-12-25T12:00:00Z"
  },
  "workflows": [
    {
      "title": "Gmail to Slack Notification",
      "description": "Send Slack notifications for new emails",
      "categories": ["Communication", "Email"],
      "node_types": ["n8n-nodes-base.gmail", "n8n-nodes-base.slack"],
      "workflow_json": { ... },
      "complexity_score": 3,
      "source": "n8n_templates"
    }
  ]
}
```

### 2. RAG Dataset (`rag_dataset_*.json`)
Optimized for retrieval-augmented generation:
```json
[
  {
    "id": "workflow_12345",
    "title": "Gmail to Slack Notification",
    "description": "Send Slack notifications for new emails",
    "use_cases": ["Email automation", "Team communication"],
    "node_types": ["Gmail", "Slack"],
    "complexity": 3,
    "workflow_json": { ... }
  }
]
```

### 3. Training Dataset (`training_dataset_*.jsonl`)
JSONL format for LLM fine-tuning:
```json
{"prompt": "Create a workflow: Gmail to Slack Notification. Requirements: Send Slack notifications for new emails", "completion": "{\"nodes\":[...],\"connections\":{...}}", "metadata": {"complexity": 3}}
```

### 4. Lightweight Dataset (`lightweight_dataset_*.json`)
Minimal format for quick prototyping:
```json
[
  {
    "title": "Gmail to Slack Notification",
    "description": "Send Slack notifications for new emails",
    "node_types": ["Gmail", "Slack"],
    "workflow_json": { ... }
  }
]
```

## 📈 Analysis Features

The analyzer provides comprehensive insights:

### Node Pattern Analysis
- Most common node types and combinations
- Parameter usage patterns
- Integration popularity

### Workflow Pattern Analysis
- Trigger patterns and distributions
- Workflow complexity analysis
- Length categorization (simple, medium, complex, enterprise)

### Use Case Extraction
- Automatic categorization by functionality
- Integration pattern identification
- Best practice extraction

## 🤖 LLM Integration

### For RAG Systems
```python
# Load RAG dataset
with open('rag_dataset_20241225_120000.json', 'r') as f:
    rag_data = json.load(f)

# Use with your vector database
for workflow in rag_data:
    # Index workflow for similarity search
    vector_db.index(workflow['description'], workflow['workflow_json'])
```

### For Fine-tuning
```python
# Load training dataset
training_data = []
with open('training_dataset_20241225_120000.jsonl', 'r') as f:
    for line in f:
        training_data.append(json.loads(line))

# Use with OpenAI fine-tuning API
openai.FineTuningJob.create(
    training_file="training_dataset.jsonl",
    model="gpt-3.5-turbo"
)
```

## ⚙️ Configuration

### Rate Limiting
```python
# Adjust delays in scrapers
DELAY_BETWEEN_REQUESTS = 2  # seconds
MAX_CONCURRENT_REQUESTS = 5
```

### Output Customization
```python
# Customize dataset creation
analyzer = WorkflowAnalyzer()
analyzer.complexity_threshold = 5
analyzer.min_node_count = 3
```

## 🔍 Monitoring and Logging

All scrapers include comprehensive logging:
- Progress tracking with timestamps
- Error handling and retry logic
- Failed URL tracking
- Performance metrics

## 📋 Best Practices

### Ethical Scraping
- Respects robots.txt and rate limits
- Uses appropriate delays between requests
- Includes proper User-Agent headers
- Handles errors gracefully

### Data Quality
- Validates workflow JSON structure
- Removes duplicates based on content similarity
- Filters out incomplete or malformed workflows
- Enriches data with metadata

### Performance
- Async processing for API calls
- Incremental saving to prevent data loss
- Memory-efficient processing of large datasets
- Parallel processing where appropriate

## 🚨 Troubleshooting

### Common Issues

**Connection Errors**
```bash
# Check network connectivity
curl -I https://n8n.io/workflows/

# Verify SSL certificates
python -c "import ssl; print(ssl.get_default_verify_paths())"
```

**Memory Issues**
```python
# Process workflows in batches
for batch in chunks(workflows, 100):
    process_batch(batch)
```

**Rate Limiting**
```python
# Increase delays
time.sleep(5)  # Wait 5 seconds between requests
```

## 📄 License

This project is for educational and research purposes. Please respect n8n's terms of service and rate limits when scraping their content.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Submit a pull request

## 📞 Support

For issues and questions:
- Check the logs for detailed error messages
- Verify network connectivity and permissions
- Review the configuration settings
- Open an issue with detailed reproduction steps
