#!/usr/bin/env python3
"""
n8n API and Community Scraper
Enhanced scraper that uses multiple sources including n8n API, GitHub, and community templates
"""

import requests
import json
import time
import re
from datetime import datetime
import logging
import asyncio
import aiohttp
from typing import List, Dict, Optional

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class N8nEnhancedScraper:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
            'Accept': 'application/json, text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        })
        self.workflows = []
        self.sources = {
            'n8n_templates': 'https://n8n.io/workflows/',
            'n8n_api': 'https://api.n8n.io/workflows',  # If available
            'github_n8n': 'https://api.github.com/search/repositories?q=n8n+workflow+language:json',
            'community_workflows': []
        }
    
    async def scrape_n8n_api_workflows(self):
        """Try to scrape from n8n's internal API endpoints"""
        api_endpoints = [
            'https://n8n.io/api/workflows',
            'https://api.n8n.io/workflows',
            'https://n8n.io/_next/static/chunks/workflows.json',
            'https://n8n.io/workflows.json'
        ]
        
        workflows = []
        
        async with aiohttp.ClientSession() as session:
            for endpoint in api_endpoints:
                try:
                    logger.info(f"Trying API endpoint: {endpoint}")
                    async with session.get(endpoint, timeout=30) as response:
                        if response.status == 200:
                            data = await response.json()
                            if isinstance(data, list):
                                workflows.extend(data)
                            elif isinstance(data, dict) and 'workflows' in data:
                                workflows.extend(data['workflows'])
                            logger.info(f"Found {len(workflows)} workflows from {endpoint}")
                except Exception as e:
                    logger.debug(f"API endpoint {endpoint} failed: {e}")
                    continue
        
        return workflows
    
    def scrape_github_n8n_workflows(self):
        """Scrape n8n workflows from GitHub repositories"""
        workflows = []
        
        try:
            # Search for n8n workflow repositories
            search_queries = [
                'n8n workflow filetype:json',
                'n8n automation workflow',
                'n8n-workflows',
                'n8n templates'
            ]
            
            for query in search_queries:
                url = f"https://api.github.com/search/code?q={query}&per_page=100"
                response = self.session.get(url)
                
                if response.status_code == 200:
                    data = response.json()
                    for item in data.get('items', []):
                        if item['name'].endswith('.json'):
                            workflow_data = self.fetch_github_workflow(item)
                            if workflow_data:
                                workflows.append(workflow_data)
                
                time.sleep(1)  # GitHub API rate limiting
        
        except Exception as e:
            logger.error(f"GitHub scraping failed: {e}")
        
        return workflows
    
    def fetch_github_workflow(self, item):
        """Fetch individual workflow from GitHub"""
        try:
            download_url = item.get('download_url')
            if not download_url:
                return None
            
            response = self.session.get(download_url)
            if response.status_code == 200:
                workflow_json = response.json()
                
                # Validate it's an n8n workflow
                if 'nodes' in workflow_json and 'connections' in workflow_json:
                    return {
                        'title': item['name'].replace('.json', ''),
                        'description': f"GitHub workflow from {item['repository']['full_name']}",
                        'source': 'github',
                        'repository': item['repository']['full_name'],
                        'workflow_json': workflow_json,
                        'node_count': len(workflow_json.get('nodes', [])),
                        'scraped_at': datetime.now().isoformat()
                    }
        except Exception as e:
            logger.debug(f"Failed to fetch GitHub workflow {item.get('name')}: {e}")
        
        return None
    
    def scrape_n8n_community_workflows(self):
        """Scrape workflows from n8n community forum"""
        workflows = []
        
        try:
            # n8n community forum workflow sharing
            community_urls = [
                'https://community.n8n.io/c/workflows/10.json',
                'https://community.n8n.io/latest.json?category=workflows'
            ]
            
            for url in community_urls:
                try:
                    response = self.session.get(url)
                    if response.status_code == 200:
                        data = response.json()
                        topics = data.get('topic_list', {}).get('topics', [])
                        
                        for topic in topics:
                            workflow_data = self.extract_community_workflow(topic)
                            if workflow_data:
                                workflows.append(workflow_data)
                
                except Exception as e:
                    logger.debug(f"Community URL {url} failed: {e}")
                    continue
        
        except Exception as e:
            logger.error(f"Community scraping failed: {e}")
        
        return workflows
    
    def extract_community_workflow(self, topic):
        """Extract workflow from community topic"""
        try:
            topic_id = topic.get('id')
            if not topic_id:
                return None
            
            # Fetch full topic content
            topic_url = f"https://community.n8n.io/t/{topic_id}.json"
            response = self.session.get(topic_url)
            
            if response.status_code == 200:
                topic_data = response.json()
                posts = topic_data.get('post_stream', {}).get('posts', [])
                
                for post in posts:
                    content = post.get('cooked', '')
                    
                    # Look for JSON workflow in post content
                    json_matches = re.findall(r'```json\s*(\{.*?\})\s*```', content, re.DOTALL)
                    for json_match in json_matches:
                        try:
                            workflow_json = json.loads(json_match)
                            if 'nodes' in workflow_json and 'connections' in workflow_json:
                                return {
                                    'title': topic.get('title', 'Community Workflow'),
                                    'description': f"Community workflow from topic {topic_id}",
                                    'source': 'community',
                                    'topic_id': topic_id,
                                    'workflow_json': workflow_json,
                                    'node_count': len(workflow_json.get('nodes', [])),
                                    'scraped_at': datetime.now().isoformat()
                                }
                        except json.JSONDecodeError:
                            continue
        
        except Exception as e:
            logger.debug(f"Failed to extract community workflow: {e}")
        
        return None
    
    def create_rag_dataset(self, workflows: List[Dict]) -> List[Dict]:
        """Create optimized dataset for RAG/LLM training"""
        rag_dataset = []
        
        for workflow in workflows:
            # Extract meaningful information for LLM
            workflow_json = workflow.get('workflow_json', {})
            nodes = workflow_json.get('nodes', [])
            connections = workflow_json.get('connections', {})
            
            # Analyze workflow structure
            node_types = [node.get('type', '') for node in nodes]
            unique_node_types = list(set(node_types))
            
            # Extract node parameters for context
            node_configs = []
            for node in nodes:
                node_config = {
                    'type': node.get('type'),
                    'name': node.get('name'),
                    'parameters': node.get('parameters', {})
                }
                node_configs.append(node_config)
            
            # Create workflow description
            workflow_description = self.generate_workflow_description(workflow, node_configs)
            
            rag_entry = {
                'id': f"{workflow.get('source', 'unknown')}_{hash(workflow.get('title', ''))}",
                'title': workflow.get('title', ''),
                'description': workflow.get('description', ''),
                'workflow_description': workflow_description,
                'categories': workflow.get('categories', []),
                'source': workflow.get('source', 'unknown'),
                'node_types': unique_node_types,
                'node_count': len(nodes),
                'connection_count': len(connections),
                'complexity_score': self.calculate_complexity_score(workflow_json),
                'use_cases': self.extract_use_cases(workflow, node_configs),
                'workflow_json': workflow_json,
                'node_configurations': node_configs,
                'created_at': workflow.get('scraped_at', datetime.now().isoformat())
            }
            
            rag_dataset.append(rag_entry)
        
        return rag_dataset
    
    def generate_workflow_description(self, workflow: Dict, node_configs: List[Dict]) -> str:
        """Generate a comprehensive workflow description for LLM understanding"""
        title = workflow.get('title', 'Workflow')
        description = workflow.get('description', '')
        
        # Analyze workflow pattern
        trigger_nodes = [n for n in node_configs if 'trigger' in n.get('type', '').lower()]
        action_nodes = [n for n in node_configs if n.get('type', '') not in [t.get('type', '') for t in trigger_nodes]]
        
        workflow_desc = f"Workflow: {title}\n"
        if description:
            workflow_desc += f"Description: {description}\n"
        
        workflow_desc += f"Structure: {len(trigger_nodes)} trigger(s), {len(action_nodes)} action(s)\n"
        
        if trigger_nodes:
            workflow_desc += f"Triggers: {', '.join([t.get('type', '') for t in trigger_nodes])}\n"
        
        if action_nodes:
            workflow_desc += f"Actions: {', '.join([a.get('type', '') for a in action_nodes[:5]])}\n"
        
        return workflow_desc
    
    def calculate_complexity_score(self, workflow_json: Dict) -> int:
        """Calculate workflow complexity score (1-10)"""
        nodes = workflow_json.get('nodes', [])
        connections = workflow_json.get('connections', {})
        
        node_count = len(nodes)
        connection_count = sum(len(conns) for conns in connections.values())
        unique_types = len(set(node.get('type', '') for node in nodes))
        
        # Simple complexity scoring
        score = min(10, (node_count // 2) + (connection_count // 3) + (unique_types // 2))
        return max(1, score)
    
    def extract_use_cases(self, workflow: Dict, node_configs: List[Dict]) -> List[str]:
        """Extract potential use cases from workflow"""
        use_cases = []
        
        # Based on node types, infer use cases
        node_types = [n.get('type', '') for n in node_configs]
        
        if any('gmail' in nt.lower() for nt in node_types):
            use_cases.append('Email automation')
        if any('slack' in nt.lower() for nt in node_types):
            use_cases.append('Team communication')
        if any('webhook' in nt.lower() for nt in node_types):
            use_cases.append('API integration')
        if any('schedule' in nt.lower() or 'cron' in nt.lower() for nt in node_types):
            use_cases.append('Scheduled automation')
        if any('database' in nt.lower() or 'mysql' in nt.lower() or 'postgres' in nt.lower() for nt in node_types):
            use_cases.append('Data processing')
        
        return use_cases
    
    async def scrape_all_sources(self):
        """Main method to scrape from all sources"""
        logger.info("Starting comprehensive n8n workflow scraping...")
        
        all_workflows = []
        
        # 1. Scrape from n8n API
        logger.info("Scraping n8n API endpoints...")
        api_workflows = await self.scrape_n8n_api_workflows()
        all_workflows.extend(api_workflows)
        
        # 2. Scrape from GitHub
        logger.info("Scraping GitHub repositories...")
        github_workflows = self.scrape_github_n8n_workflows()
        all_workflows.extend(github_workflows)
        
        # 3. Scrape from community
        logger.info("Scraping n8n community...")
        community_workflows = self.scrape_n8n_community_workflows()
        all_workflows.extend(community_workflows)
        
        logger.info(f"Total workflows collected: {len(all_workflows)}")
        
        # Create RAG dataset
        rag_dataset = self.create_rag_dataset(all_workflows)
        
        return all_workflows, rag_dataset
    
    def save_datasets(self, workflows: List[Dict], rag_dataset: List[Dict]):
        """Save the collected datasets"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Save complete dataset
        complete_file = f"n8n_workflows_enhanced_{timestamp}.json"
        with open(complete_file, 'w', encoding='utf-8') as f:
            json.dump({
                'metadata': {
                    'total_workflows': len(workflows),
                    'sources': ['n8n_api', 'github', 'community'],
                    'scraped_at': datetime.now().isoformat()
                },
                'workflows': workflows
            }, f, indent=2, ensure_ascii=False)
        
        # Save RAG-optimized dataset
        rag_file = f"n8n_workflows_rag_enhanced_{timestamp}.json"
        with open(rag_file, 'w', encoding='utf-8') as f:
            json.dump(rag_dataset, f, indent=2, ensure_ascii=False)
        
        logger.info(f"Enhanced dataset saved to {complete_file}")
        logger.info(f"RAG dataset saved to {rag_file}")
        
        return complete_file, rag_file

async def main():
    scraper = N8nEnhancedScraper()
    
    try:
        workflows, rag_dataset = await scraper.scrape_all_sources()
        complete_file, rag_file = scraper.save_datasets(workflows, rag_dataset)
        
        print(f"\n🎉 Enhanced scraping completed!")
        print(f"📊 Total workflows: {len(workflows)}")
        print(f"🤖 RAG entries: {len(rag_dataset)}")
        print(f"💾 Complete dataset: {complete_file}")
        print(f"🧠 RAG dataset: {rag_file}")
        
    except Exception as e:
        logger.error(f"Scraping failed: {e}")

if __name__ == "__main__":
    asyncio.run(main())
