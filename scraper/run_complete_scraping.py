#!/usr/bin/env python3
"""
Complete n8n Workflow Scraping Orchestrator
Runs all scraping methods and creates comprehensive datasets for LLM training
"""

import asyncio
import json
import os
import sys
from datetime import datetime
import logging

# Add current directory to path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from n8n_template_scraper import N8nTemplateScraper
from n8n_api_scraper import N8nEnhancedScraper
from workflow_analyzer import WorkflowAnalyzer

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class CompleteScraper:
    def __init__(self, output_dir: str = "complete_scraping_results"):
        self.output_dir = output_dir
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.all_workflows = []
        
        # Create output directory
        os.makedirs(output_dir, exist_ok=True)
        
    async def run_complete_scraping(self):
        """Run all scraping methods and create comprehensive datasets"""
        logger.info("🚀 Starting complete n8n workflow scraping...")
        
        # 1. Scrape n8n template pages
        logger.info("📄 Scraping n8n template pages...")
        template_workflows = await self.scrape_template_pages()
        
        # 2. Scrape enhanced sources (API, GitHub, Community)
        logger.info("🔍 Scraping enhanced sources...")
        enhanced_workflows = await self.scrape_enhanced_sources()
        
        # 3. Combine all workflows
        self.all_workflows = template_workflows + enhanced_workflows
        logger.info(f"📊 Total workflows collected: {len(self.all_workflows)}")
        
        # 4. Deduplicate workflows
        deduplicated_workflows = self.deduplicate_workflows(self.all_workflows)
        logger.info(f"🔄 After deduplication: {len(deduplicated_workflows)}")
        
        # 5. Analyze workflows and create datasets
        logger.info("📈 Analyzing workflows...")
        analysis_results = await self.analyze_workflows(deduplicated_workflows)
        
        # 6. Create final datasets
        logger.info("💾 Creating final datasets...")
        final_datasets = self.create_final_datasets(deduplicated_workflows, analysis_results)
        
        # 7. Generate summary report
        self.generate_summary_report(deduplicated_workflows, analysis_results, final_datasets)
        
        return final_datasets
    
    async def scrape_template_pages(self):
        """Scrape n8n template pages"""
        try:
            scraper = N8nTemplateScraper()
            workflows = scraper.scrape_all_workflows()
            
            # Save intermediate results
            template_file = os.path.join(self.output_dir, f"template_workflows_{self.timestamp}.json")
            with open(template_file, 'w', encoding='utf-8') as f:
                json.dump(workflows, f, indent=2, ensure_ascii=False)
            
            logger.info(f"✅ Template scraping completed: {len(workflows)} workflows")
            return workflows
            
        except Exception as e:
            logger.error(f"❌ Template scraping failed: {e}")
            return []
    
    async def scrape_enhanced_sources(self):
        """Scrape enhanced sources (API, GitHub, Community)"""
        try:
            scraper = N8nEnhancedScraper()
            workflows, rag_dataset = await scraper.scrape_all_sources()
            
            # Save intermediate results
            enhanced_file = os.path.join(self.output_dir, f"enhanced_workflows_{self.timestamp}.json")
            with open(enhanced_file, 'w', encoding='utf-8') as f:
                json.dump(workflows, f, indent=2, ensure_ascii=False)
            
            logger.info(f"✅ Enhanced scraping completed: {len(workflows)} workflows")
            return workflows
            
        except Exception as e:
            logger.error(f"❌ Enhanced scraping failed: {e}")
            return []
    
    def deduplicate_workflows(self, workflows):
        """Remove duplicate workflows based on content similarity"""
        seen_workflows = {}
        deduplicated = []
        
        for workflow in workflows:
            # Create a hash based on workflow structure
            workflow_json = workflow.get('workflow_json', {})
            nodes = workflow_json.get('nodes', [])
            
            # Create signature based on node types and count
            node_types = sorted([node.get('type', '') for node in nodes])
            signature = f"{len(nodes)}_{hash(tuple(node_types))}"
            
            if signature not in seen_workflows:
                seen_workflows[signature] = workflow
                deduplicated.append(workflow)
            else:
                # Keep the one with more metadata
                existing = seen_workflows[signature]
                if len(workflow.get('description', '')) > len(existing.get('description', '')):
                    seen_workflows[signature] = workflow
                    # Replace in deduplicated list
                    for i, w in enumerate(deduplicated):
                        if w == existing:
                            deduplicated[i] = workflow
                            break
        
        return deduplicated
    
    async def analyze_workflows(self, workflows):
        """Analyze workflows and create training datasets"""
        analyzer = WorkflowAnalyzer()
        analyzer.workflows = workflows
        
        # Create analysis directory
        analysis_dir = os.path.join(self.output_dir, "analysis")
        results = analyzer.save_analysis_results(analysis_dir)
        
        return results
    
    def create_final_datasets(self, workflows, analysis_results):
        """Create final optimized datasets"""
        datasets = {}
        
        # 1. Complete dataset with all metadata
        complete_dataset = {
            'metadata': {
                'total_workflows': len(workflows),
                'created_at': datetime.now().isoformat(),
                'sources': ['n8n_templates', 'github', 'community', 'api'],
                'analysis_stats': analysis_results
            },
            'workflows': workflows
        }
        
        complete_file = os.path.join(self.output_dir, f"complete_dataset_{self.timestamp}.json")
        with open(complete_file, 'w', encoding='utf-8') as f:
            json.dump(complete_dataset, f, indent=2, ensure_ascii=False)
        datasets['complete'] = complete_file
        
        # 2. RAG-optimized dataset
        rag_dataset = []
        for workflow in workflows:
            rag_entry = {
                'id': f"workflow_{hash(workflow.get('title', ''))}",
                'title': workflow.get('title', ''),
                'description': workflow.get('description', ''),
                'categories': workflow.get('categories', []),
                'node_types': workflow.get('node_types', []),
                'use_cases': workflow.get('use_cases', []),
                'complexity': workflow.get('complexity_score', 1),
                'workflow_json': workflow.get('workflow_json', {}),
                'source': workflow.get('source', 'unknown')
            }
            rag_dataset.append(rag_entry)
        
        rag_file = os.path.join(self.output_dir, f"rag_dataset_{self.timestamp}.json")
        with open(rag_file, 'w', encoding='utf-8') as f:
            json.dump(rag_dataset, f, indent=2, ensure_ascii=False)
        datasets['rag'] = rag_file
        
        # 3. Training dataset (JSONL format for fine-tuning)
        training_file = os.path.join(self.output_dir, f"training_dataset_{self.timestamp}.jsonl")
        with open(training_file, 'w', encoding='utf-8') as f:
            for workflow in workflows:
                training_example = {
                    'prompt': self.create_training_prompt(workflow),
                    'completion': json.dumps(workflow.get('workflow_json', {}), separators=(',', ':')),
                    'metadata': {
                        'title': workflow.get('title', ''),
                        'complexity': workflow.get('complexity_score', 1)
                    }
                }
                f.write(json.dumps(training_example) + '\n')
        datasets['training'] = training_file
        
        # 4. Lightweight dataset (just essential info)
        lightweight_dataset = []
        for workflow in workflows:
            lightweight_entry = {
                'title': workflow.get('title', ''),
                'description': workflow.get('description', ''),
                'node_types': workflow.get('node_types', []),
                'workflow_json': workflow.get('workflow_json', {})
            }
            lightweight_dataset.append(lightweight_entry)
        
        lightweight_file = os.path.join(self.output_dir, f"lightweight_dataset_{self.timestamp}.json")
        with open(lightweight_file, 'w', encoding='utf-8') as f:
            json.dump(lightweight_dataset, f, separators=(',', ':'), ensure_ascii=False)
        datasets['lightweight'] = lightweight_file
        
        return datasets
    
    def create_training_prompt(self, workflow):
        """Create a training prompt for the workflow"""
        title = workflow.get('title', '')
        description = workflow.get('description', '')
        categories = workflow.get('categories', [])
        node_types = workflow.get('node_types', [])
        
        prompt_parts = []
        
        if title:
            prompt_parts.append(f"Create a workflow: {title}")
        
        if description:
            prompt_parts.append(f"Requirements: {description}")
        
        if categories:
            prompt_parts.append(f"Categories: {', '.join(categories)}")
        
        if node_types:
            prompt_parts.append(f"Use these integrations: {', '.join(node_types[:5])}")
        
        return ". ".join(prompt_parts)
    
    def generate_summary_report(self, workflows, analysis_results, datasets):
        """Generate a comprehensive summary report"""
        report = {
            'scraping_summary': {
                'timestamp': self.timestamp,
                'total_workflows': len(workflows),
                'sources_breakdown': self.get_sources_breakdown(workflows),
                'complexity_distribution': self.get_complexity_distribution(workflows),
                'top_node_types': self.get_top_node_types(workflows),
                'top_categories': self.get_top_categories(workflows)
            },
            'analysis_results': analysis_results,
            'datasets_created': {
                'complete_dataset': os.path.basename(datasets['complete']),
                'rag_dataset': os.path.basename(datasets['rag']),
                'training_dataset': os.path.basename(datasets['training']),
                'lightweight_dataset': os.path.basename(datasets['lightweight'])
            },
            'usage_recommendations': {
                'rag_dataset': 'Use for retrieval-augmented generation in your LLM',
                'training_dataset': 'Use for fine-tuning OpenAI models or other LLMs',
                'complete_dataset': 'Use for comprehensive analysis and research',
                'lightweight_dataset': 'Use for quick prototyping and testing'
            }
        }
        
        report_file = os.path.join(self.output_dir, f"scraping_report_{self.timestamp}.json")
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        # Also create a human-readable report
        self.create_human_readable_report(report)
        
        return report
    
    def get_sources_breakdown(self, workflows):
        """Get breakdown of workflows by source"""
        sources = {}
        for workflow in workflows:
            source = workflow.get('source', 'unknown')
            sources[source] = sources.get(source, 0) + 1
        return sources
    
    def get_complexity_distribution(self, workflows):
        """Get distribution of workflow complexity"""
        complexity = {}
        for workflow in workflows:
            comp = workflow.get('complexity_score', 1)
            complexity[f"level_{comp}"] = complexity.get(f"level_{comp}", 0) + 1
        return complexity
    
    def get_top_node_types(self, workflows):
        """Get most common node types"""
        node_types = {}
        for workflow in workflows:
            for node_type in workflow.get('node_types', []):
                node_types[node_type] = node_types.get(node_type, 0) + 1
        return dict(sorted(node_types.items(), key=lambda x: x[1], reverse=True)[:20])
    
    def get_top_categories(self, workflows):
        """Get most common categories"""
        categories = {}
        for workflow in workflows:
            for category in workflow.get('categories', []):
                categories[category] = categories.get(category, 0) + 1
        return dict(sorted(categories.items(), key=lambda x: x[1], reverse=True)[:15])
    
    def create_human_readable_report(self, report):
        """Create a human-readable markdown report"""
        report_md = f"""# n8n Workflow Scraping Report
Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## Summary
- **Total Workflows**: {report['scraping_summary']['total_workflows']}
- **Sources**: {', '.join(report['scraping_summary']['sources_breakdown'].keys())}
- **Analysis Results**: {report['analysis_results']}

## Source Breakdown
"""
        
        for source, count in report['scraping_summary']['sources_breakdown'].items():
            report_md += f"- **{source}**: {count} workflows\n"
        
        report_md += "\n## Top Node Types\n"
        for node_type, count in list(report['scraping_summary']['top_node_types'].items())[:10]:
            report_md += f"- **{node_type}**: {count} uses\n"
        
        report_md += "\n## Datasets Created\n"
        for dataset_type, filename in report['datasets_created'].items():
            usage = report['usage_recommendations'][dataset_type]
            report_md += f"- **{filename}**: {usage}\n"
        
        report_file = os.path.join(self.output_dir, f"scraping_report_{self.timestamp}.md")
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report_md)

async def main():
    """Main execution function"""
    print("🚀 Starting Complete n8n Workflow Scraping...")
    print("This will scrape workflows from multiple sources and create comprehensive datasets.")
    print("⚠️  This process may take 30-60 minutes depending on the number of workflows.")
    
    # Confirm before starting
    response = input("\nDo you want to continue? (y/N): ")
    if response.lower() != 'y':
        print("Scraping cancelled.")
        return
    
    scraper = CompleteScraper()
    
    try:
        datasets = await scraper.run_complete_scraping()
        
        print(f"\n🎉 Scraping completed successfully!")
        print(f"📁 Results saved to: {scraper.output_dir}")
        print(f"\n📊 Datasets created:")
        for dataset_type, filepath in datasets.items():
            print(f"  - {dataset_type}: {os.path.basename(filepath)}")
        
        print(f"\n🤖 Ready for LLM training!")
        print(f"Use the RAG dataset for retrieval-augmented generation")
        print(f"Use the training dataset for fine-tuning models")
        
    except KeyboardInterrupt:
        print("\n⏹️  Scraping interrupted by user")
    except Exception as e:
        print(f"\n❌ Scraping failed: {e}")
        logger.error(f"Complete scraping failed: {e}")

if __name__ == "__main__":
    asyncio.run(main())
