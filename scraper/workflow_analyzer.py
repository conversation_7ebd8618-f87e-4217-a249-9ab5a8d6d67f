#!/usr/bin/env python3
"""
n8n Workflow Analyzer and RAG Dataset Creator
Analyzes scraped workflows and creates optimized datasets for LLM training
"""

import json
import re
from collections import Counter, defaultdict
from datetime import datetime
from typing import Dict, List, Set, Tuple
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class WorkflowAnalyzer:
    def __init__(self):
        self.workflows = []
        self.node_patterns = defaultdict(list)
        self.workflow_patterns = defaultdict(list)
        self.integration_patterns = defaultdict(list)
        
    def load_workflows(self, file_path: str):
        """Load workflows from JSON file"""
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
            if isinstance(data, dict) and 'workflows' in data:
                self.workflows = data['workflows']
            elif isinstance(data, list):
                self.workflows = data
            else:
                self.workflows = [data]
        
        logger.info(f"Loaded {len(self.workflows)} workflows")
    
    def analyze_node_patterns(self):
        """Analyze common node patterns and configurations"""
        node_types = Counter()
        node_combinations = Counter()
        parameter_patterns = defaultdict(Counter)
        
        for workflow in self.workflows:
            workflow_json = workflow.get('workflow_json', {})
            nodes = workflow_json.get('nodes', [])
            
            # Count node types
            workflow_node_types = []
            for node in nodes:
                node_type = node.get('type', '')
                node_types[node_type] += 1
                workflow_node_types.append(node_type)
                
                # Analyze parameters
                params = node.get('parameters', {})
                for param_key, param_value in params.items():
                    if isinstance(param_value, (str, int, bool)):
                        parameter_patterns[node_type][param_key] += 1
            
            # Count node combinations (sequences)
            for i in range(len(workflow_node_types) - 1):
                combo = f"{workflow_node_types[i]} -> {workflow_node_types[i+1]}"
                node_combinations[combo] += 1
        
        return {
            'node_types': dict(node_types.most_common(50)),
            'node_combinations': dict(node_combinations.most_common(30)),
            'parameter_patterns': dict(parameter_patterns)
        }
    
    def analyze_workflow_patterns(self):
        """Analyze high-level workflow patterns"""
        patterns = {
            'trigger_patterns': Counter(),
            'workflow_lengths': Counter(),
            'complexity_distribution': Counter(),
            'integration_patterns': Counter()
        }
        
        for workflow in self.workflows:
            workflow_json = workflow.get('workflow_json', {})
            nodes = workflow_json.get('nodes', [])
            
            # Trigger patterns
            triggers = [n for n in nodes if 'trigger' in n.get('type', '').lower()]
            if triggers:
                trigger_type = triggers[0].get('type', '')
                patterns['trigger_patterns'][trigger_type] += 1
            
            # Workflow length
            length_category = self.categorize_workflow_length(len(nodes))
            patterns['workflow_lengths'][length_category] += 1
            
            # Complexity
            complexity = workflow.get('complexity_score', self.calculate_complexity(workflow_json))
            complexity_category = f"complexity_{complexity//2*2}-{complexity//2*2+1}"
            patterns['complexity_distribution'][complexity_category] += 1
            
            # Integration patterns
            integrations = self.extract_integrations(nodes)
            for integration in integrations:
                patterns['integration_patterns'][integration] += 1
        
        return {k: dict(v.most_common(20)) for k, v in patterns.items()}
    
    def categorize_workflow_length(self, node_count: int) -> str:
        """Categorize workflow by node count"""
        if node_count <= 3:
            return "simple (1-3 nodes)"
        elif node_count <= 7:
            return "medium (4-7 nodes)"
        elif node_count <= 15:
            return "complex (8-15 nodes)"
        else:
            return "enterprise (15+ nodes)"
    
    def calculate_complexity(self, workflow_json: Dict) -> int:
        """Calculate workflow complexity score"""
        nodes = workflow_json.get('nodes', [])
        connections = workflow_json.get('connections', {})
        
        node_count = len(nodes)
        connection_count = sum(len(conns) for conns in connections.values())
        unique_types = len(set(node.get('type', '') for node in nodes))
        
        # Complexity factors
        complexity = 0
        complexity += node_count * 0.5
        complexity += connection_count * 0.3
        complexity += unique_types * 0.8
        
        # Bonus for advanced nodes
        advanced_nodes = ['n8n-nodes-base.code', 'n8n-nodes-base.function', 'n8n-nodes-base.httpRequest']
        for node in nodes:
            if node.get('type') in advanced_nodes:
                complexity += 1
        
        return min(10, max(1, int(complexity)))
    
    def extract_integrations(self, nodes: List[Dict]) -> List[str]:
        """Extract integration services from nodes"""
        integrations = []
        
        for node in nodes:
            node_type = node.get('type', '')
            
            # Extract service name from node type
            if 'n8n-nodes-base.' in node_type:
                service = node_type.replace('n8n-nodes-base.', '')
                # Clean up service names
                service = re.sub(r'([A-Z])', r' \1', service).strip().title()
                integrations.append(service)
        
        return list(set(integrations))
    
    def create_training_examples(self) -> List[Dict]:
        """Create training examples for LLM fine-tuning"""
        training_examples = []
        
        for workflow in self.workflows:
            workflow_json = workflow.get('workflow_json', {})
            
            # Create prompt-completion pairs
            prompt = self.generate_workflow_prompt(workflow)
            completion = json.dumps(workflow_json, separators=(',', ':'))
            
            training_example = {
                'prompt': prompt,
                'completion': completion,
                'metadata': {
                    'title': workflow.get('title', ''),
                    'source': workflow.get('source', ''),
                    'node_count': len(workflow_json.get('nodes', [])),
                    'complexity': workflow.get('complexity_score', 1)
                }
            }
            
            training_examples.append(training_example)
        
        return training_examples
    
    def generate_workflow_prompt(self, workflow: Dict) -> str:
        """Generate a natural language prompt for the workflow"""
        title = workflow.get('title', '')
        description = workflow.get('description', '')
        workflow_json = workflow.get('workflow_json', {})
        nodes = workflow_json.get('nodes', [])
        
        # Extract key information
        integrations = self.extract_integrations(nodes)
        node_count = len(nodes)
        
        # Generate prompt
        prompt_parts = []
        
        if title:
            prompt_parts.append(f"Create a workflow: {title}")
        
        if description:
            prompt_parts.append(f"Description: {description}")
        
        if integrations:
            prompt_parts.append(f"Integrations: {', '.join(integrations[:5])}")
        
        prompt_parts.append(f"Complexity: {self.categorize_workflow_length(node_count)}")
        
        return ". ".join(prompt_parts)
    
    def create_rag_knowledge_base(self) -> Dict:
        """Create a comprehensive knowledge base for RAG"""
        knowledge_base = {
            'workflow_templates': [],
            'node_documentation': {},
            'pattern_library': {},
            'use_case_examples': {},
            'best_practices': []
        }
        
        # Workflow templates
        for workflow in self.workflows:
            template = {
                'id': workflow.get('title', '').lower().replace(' ', '_'),
                'title': workflow.get('title', ''),
                'description': workflow.get('description', ''),
                'categories': workflow.get('categories', []),
                'use_cases': workflow.get('use_cases', []),
                'node_types': workflow.get('node_types', []),
                'complexity': workflow.get('complexity_score', 1),
                'workflow_json': workflow.get('workflow_json', {}),
                'prompt_examples': [
                    self.generate_workflow_prompt(workflow),
                    f"Build an automation for {workflow.get('title', '').lower()}",
                    f"Create a workflow to {workflow.get('description', '').lower()}"
                ]
            }
            knowledge_base['workflow_templates'].append(template)
        
        # Node documentation
        node_patterns = self.analyze_node_patterns()
        for node_type, count in node_patterns['node_types'].items():
            knowledge_base['node_documentation'][node_type] = {
                'usage_count': count,
                'common_parameters': list(node_patterns['parameter_patterns'].get(node_type, {}).keys()),
                'description': self.get_node_description(node_type)
            }
        
        # Pattern library
        workflow_patterns = self.analyze_workflow_patterns()
        knowledge_base['pattern_library'] = workflow_patterns
        
        # Use case examples
        use_cases = defaultdict(list)
        for workflow in self.workflows:
            for use_case in workflow.get('use_cases', []):
                use_cases[use_case].append({
                    'title': workflow.get('title', ''),
                    'workflow_json': workflow.get('workflow_json', {})
                })
        
        knowledge_base['use_case_examples'] = dict(use_cases)
        
        return knowledge_base
    
    def get_node_description(self, node_type: str) -> str:
        """Get description for a node type"""
        descriptions = {
            'n8n-nodes-base.webhook': 'Receives HTTP requests to trigger workflows',
            'n8n-nodes-base.httpRequest': 'Makes HTTP requests to external APIs',
            'n8n-nodes-base.gmail': 'Integrates with Gmail for email operations',
            'n8n-nodes-base.slack': 'Sends messages and interacts with Slack',
            'n8n-nodes-base.googleSheets': 'Reads and writes data to Google Sheets',
            'n8n-nodes-base.schedule': 'Triggers workflows on a schedule',
            'n8n-nodes-base.code': 'Executes custom JavaScript code',
            'n8n-nodes-base.if': 'Conditional logic for workflow branching',
            'n8n-nodes-base.set': 'Sets or modifies data values',
            'n8n-nodes-base.merge': 'Combines data from multiple sources'
        }
        
        return descriptions.get(node_type, f"n8n node: {node_type}")
    
    def save_analysis_results(self, output_dir: str = "analysis_results"):
        """Save all analysis results"""
        import os
        os.makedirs(output_dir, exist_ok=True)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Node patterns
        node_patterns = self.analyze_node_patterns()
        with open(f"{output_dir}/node_patterns_{timestamp}.json", 'w') as f:
            json.dump(node_patterns, f, indent=2)
        
        # Workflow patterns
        workflow_patterns = self.analyze_workflow_patterns()
        with open(f"{output_dir}/workflow_patterns_{timestamp}.json", 'w') as f:
            json.dump(workflow_patterns, f, indent=2)
        
        # Training examples
        training_examples = self.create_training_examples()
        with open(f"{output_dir}/training_examples_{timestamp}.jsonl", 'w') as f:
            for example in training_examples:
                f.write(json.dumps(example) + '\n')
        
        # RAG knowledge base
        knowledge_base = self.create_rag_knowledge_base()
        with open(f"{output_dir}/rag_knowledge_base_{timestamp}.json", 'w') as f:
            json.dump(knowledge_base, f, indent=2)
        
        logger.info(f"Analysis results saved to {output_dir}/")
        
        return {
            'node_patterns': len(node_patterns['node_types']),
            'workflow_patterns': len(workflow_patterns),
            'training_examples': len(training_examples),
            'knowledge_base_entries': len(knowledge_base['workflow_templates'])
        }

def main():
    analyzer = WorkflowAnalyzer()
    
    # Load workflows (you'll need to provide the path to your scraped data)
    # analyzer.load_workflows('scraped_data/n8n_workflows_complete_20241225_120000.json')
    
    # For demo, create some sample data
    sample_workflows = [
        {
            'title': 'Gmail to Slack Notification',
            'description': 'Send Slack notifications for new Gmail emails',
            'categories': ['Communication', 'Email'],
            'workflow_json': {
                'nodes': [
                    {'type': 'n8n-nodes-base.gmail', 'name': 'Gmail Trigger'},
                    {'type': 'n8n-nodes-base.slack', 'name': 'Send Slack Message'}
                ],
                'connections': {'Gmail Trigger': {'main': [['Send Slack Message']]}}
            }
        }
    ]
    
    analyzer.workflows = sample_workflows
    results = analyzer.save_analysis_results()
    
    print(f"Analysis completed:")
    print(f"- Node patterns: {results['node_patterns']}")
    print(f"- Workflow patterns: {results['workflow_patterns']}")
    print(f"- Training examples: {results['training_examples']}")
    print(f"- Knowledge base entries: {results['knowledge_base_entries']}")

if __name__ == "__main__":
    main()
