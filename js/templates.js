// Advanced Workflow Templates - Categorized by Use Case
export const TEMPLATE_CATEGORIES = {
    "AI Agents & Automation": [
        {
            title: "AI Customer Support Agent",
            description: "Intelligent ticket management with sentiment analysis",
            prompt: "Create an AI customer support agent that monitors support tickets in Zendesk, analyzes sentiment using OpenAI GPT-4, categorizes issues by urgency and type, auto-responds to simple queries with personalized messages, escalates complex issues to human agents with context summaries, tracks resolution metrics in a dashboard, and learns from successful resolutions to improve future responses"
        },
        {
            title: "AI Content Creation Pipeline",
            description: "Automated content generation and distribution",
            prompt: "Build an AI content creation pipeline that monitors trending topics from Twitter API and Google Trends, generates blog post outlines using GPT-4, creates SEO-optimized content with keyword research, generates accompanying social media posts for multiple platforms, schedules publication across WordPress and social channels, tracks engagement metrics, and optimizes content strategy based on performance data"
        },
        {
            title: "AI Sales Assistant",
            description: "Intelligent lead management and outreach",
            prompt: "Design an AI sales assistant that monitors new leads from multiple sources (website forms, LinkedIn, cold emails), enriches lead data using Clearbit and ZoomInfo APIs, scores leads with AI analysis based on company size and behavior, personalizes outreach messages using GPT-4, schedules follow-ups based on engagement, updates CRM with interaction history, and provides sales team with AI-generated talking points"
        },
        {
            title: "AI Recruitment Agent",
            description: "Automated candidate screening and interview scheduling",
            prompt: "Develop an AI-powered recruitment workflow that scrapes job applications from multiple platforms (LinkedIn, Indeed, company website), uses AI to screen resumes against job requirements, conducts initial video interview analysis using sentiment and speech analysis, ranks candidates with scoring algorithms, sends personalized rejection/acceptance emails, schedules interviews with hiring managers, and tracks recruitment funnel metrics"
        },
        {
            title: "AI Financial Analyst",
            description: "Market analysis and investment recommendations",
            prompt: "Create an AI financial analyst agent that monitors market data APIs (Yahoo Finance, Alpha Vantage), analyzes news sentiment using NLP, generates investment recommendations based on technical and fundamental analysis, creates automated trading signals with risk management, sends risk alerts for portfolio positions, produces daily market analysis reports for stakeholders, and tracks prediction accuracy over time"
        }
    ],

    "E-commerce & Retail": [
        {
            title: "Advanced Order Fulfillment System",
            description: "End-to-end e-commerce automation",
            prompt: "Build a comprehensive e-commerce order fulfillment system that processes orders from Shopify and WooCommerce, validates inventory across multiple warehouses using real-time APIs, calculates optimal shipping routes and costs, generates shipping labels automatically, sends tracking notifications via email and SMS, handles returns processing with automated RMA generation, updates financial records in QuickBooks, and provides real-time fulfillment analytics"
        },
        {
            title: "Dynamic Pricing Engine",
            description: "AI-powered competitive pricing strategy",
            prompt: "Create a dynamic pricing engine that monitors competitor prices across multiple platforms, analyzes market demand patterns, adjusts product prices based on inventory levels and sales velocity, implements time-based pricing strategies (flash sales, seasonal adjustments), tracks price elasticity and conversion rates, sends pricing alerts to management, and generates pricing optimization reports with ROI analysis"
        },
        {
            title: "Inventory Optimization System",
            description: "Predictive inventory management",
            prompt: "Design an advanced inventory management system that monitors stock levels across multiple locations, predicts demand using historical data and ML algorithms, automatically reorders products with optimal quantities, optimizes supplier selection based on cost and delivery performance, tracks delivery performance and quality metrics, handles quality control workflows with defect tracking, and generates procurement analytics with cost savings reports"
        }
    ],

    "SaaS & Technology": [
        {
            title: "Multi-Tenant Customer Onboarding",
            description: "Automated SaaS customer lifecycle",
            prompt: "Design a multi-tenant SaaS customer onboarding workflow that triggers on new Stripe subscriptions, creates user accounts in Auth0 with proper role assignments, provisions resources in AWS with tenant isolation, sends personalized welcome email sequences based on plan type, schedules onboarding calls with customer success, tracks feature adoption and usage metrics, handles trial-to-paid conversions with automated billing, and manages subscription lifecycle events"
        },
        {
            title: "DevOps CI/CD Pipeline",
            description: "Automated development workflow",
            prompt: "Build a comprehensive project management automation that monitors GitHub commits and pull requests, runs automated tests with coverage reporting, deploys to staging environments with blue-green deployment, notifies stakeholders via Slack with deployment status, creates JIRA tickets for failed tests or bugs, generates release notes from commit messages, schedules production deployments with approval workflows, tracks deployment success metrics and rollback procedures"
        },
        {
            title: "Customer Churn Prevention",
            description: "Proactive retention automation",
            prompt: "Design a sophisticated customer churn prevention system that analyzes user behavior patterns and engagement metrics, identifies at-risk customers using ML models and predictive analytics, triggers personalized retention campaigns via email and in-app messaging, schedules intervention calls with customer success team, offers targeted discounts and feature upgrades, measures retention success rates and campaign effectiveness, and provides churn prediction dashboards for management"
        }
    ],

    "Data & Analytics": [
        {
            title: "Real-Time IoT Data Pipeline",
            description: "Industrial IoT monitoring and analytics",
            prompt: "Create a real-time data pipeline that ingests streaming data from IoT sensors using MQTT and HTTP endpoints, processes data using Apache Kafka for stream processing, applies ML models for anomaly detection and pattern recognition, triggers alerts for critical issues via multiple channels, stores processed data in time-series databases (InfluxDB), generates predictive maintenance schedules based on equipment health, creates executive dashboards with real-time KPIs, and provides API endpoints for third-party integrations"
        },
        {
            title: "Social Media Intelligence Platform",
            description: "Brand monitoring and competitive analysis",
            prompt: "Build a comprehensive social media intelligence system that monitors brand mentions across platforms (Twitter, Facebook, Instagram, Reddit), analyzes sentiment and trends using NLP, identifies influencers and key conversations, generates competitive analysis reports with market share insights, triggers crisis management protocols for negative sentiment spikes, provides real-time social media ROI metrics, tracks campaign performance across channels, and creates automated social listening reports"
        },
        {
            title: "Fraud Detection Engine",
            description: "Real-time transaction monitoring",
            prompt: "Create a sophisticated fraud detection workflow that monitors transactions in real-time from multiple payment processors, applies ML models for risk scoring based on user behavior and transaction patterns, cross-references with blacklists and external fraud APIs, triggers manual review workflows for suspicious activities, automatically blocks high-risk transactions with configurable thresholds, generates compliance reports for regulatory requirements, tracks false positive rates, and provides fraud analytics dashboards"
        }
    ],

    "Healthcare & Life Sciences": [
        {
            title: "Patient Management System",
            description: "Comprehensive healthcare workflow",
            prompt: "Design a healthcare patient management workflow that monitors patient data from multiple sources (EHR, wearables, lab results), schedules appointments based on urgency and provider availability, sends automated reminders via preferred communication channels, processes insurance claims with automated verification, tracks treatment outcomes and medication adherence, ensures HIPAA compliance with audit trails, generates clinical reports for providers, and provides patient portal integration with secure messaging"
        },
        {
            title: "Clinical Trial Management",
            description: "Research and compliance automation",
            prompt: "Create a clinical trial management system that screens potential participants based on inclusion/exclusion criteria, schedules and tracks study visits with automated reminders, manages informed consent processes with digital signatures, monitors adverse events with automated reporting to regulatory bodies, tracks protocol deviations and corrective actions, generates regulatory compliance reports, manages study drug inventory and randomization, and provides real-time trial progress dashboards"
        }
    ],

    "Financial Services": [
        {
            title: "Automated Trading System",
            description: "Algorithmic trading with risk management",
            prompt: "Build an automated trading system that monitors multiple market data feeds in real-time, applies technical analysis algorithms and ML models for signal generation, executes trades through broker APIs with position sizing algorithms, implements comprehensive risk management with stop-losses and position limits, tracks portfolio performance with attribution analysis, generates regulatory compliance reports, provides real-time P&L monitoring, and includes backtesting capabilities for strategy validation"
        },
        {
            title: "Loan Processing Automation",
            description: "End-to-end lending workflow",
            prompt: "Design a loan processing automation that captures applications from multiple channels, performs automated credit checks and income verification, calculates loan terms and pricing based on risk models, generates loan documents with digital signature workflows, manages underwriting review processes with automated decision trees, handles loan servicing with payment processing and collections, tracks regulatory compliance requirements, and provides loan portfolio analytics and reporting"
        }
    ]
};

// Export flattened templates for backward compatibility
export const COMPLEX_TEMPLATES = Object.values(TEMPLATE_CATEGORIES)
    .flat()
    .map(template => template.prompt);

// Template search and filtering utilities
export class TemplateManager {
    static searchTemplates(query) {
        const results = [];
        Object.entries(TEMPLATE_CATEGORIES).forEach(([category, templates]) => {
            templates.forEach(template => {
                if (
                    template.title.toLowerCase().includes(query.toLowerCase()) ||
                    template.description.toLowerCase().includes(query.toLowerCase()) ||
                    template.prompt.toLowerCase().includes(query.toLowerCase())
                ) {
                    results.push({ ...template, category });
                }
            });
        });
        return results;
    }

    static getTemplatesByCategory(category) {
        return TEMPLATE_CATEGORIES[category] || [];
    }

    static getAllCategories() {
        return Object.keys(TEMPLATE_CATEGORIES);
    }

    static getRandomTemplate() {
        const allTemplates = Object.values(TEMPLATE_CATEGORIES).flat();
        return allTemplates[Math.floor(Math.random() * allTemplates.length)];
    }
}
