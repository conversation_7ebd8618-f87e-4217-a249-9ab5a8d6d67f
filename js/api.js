// API Service - OpenAI Assistant Integration
import { CONFIG } from './config.js';

export class APIService {
    constructor() {
        this.baseURL = CONFIG.OPENAI_API_BASE_URL;
        this.apiKey = CONFIG.OPENAI_API_KEY;
        this.assistantId = CONFIG.ASSISTANT_ID;
    }

    // Create a new thread
    async createThread() {
        const response = await fetch(`${this.baseURL}/threads`, {
            method: "POST",
            headers: {
                "Authorization": `Bearer ${this.apiKey}`,
                "OpenAI-Beta": "assistants=v2",
                "Content-Type": "application/json"
            },
            body: JSON.stringify({})
        });

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(`Error creating thread: ${response.status} - ${errorData.message || JSON.stringify(errorData)}`);
        }

        return await response.json();
    }

    // Add message to thread
    async addMessage(threadId, content) {
        const response = await fetch(`${this.baseURL}/threads/${threadId}/messages`, {
            method: "POST",
            headers: {
                "Authorization": `Bearer ${this.apiKey}`,
                "OpenAI-Beta": "assistants=v2",
                "Content-Type": "application/json"
            },
            body: JSON.stringify({
                role: "user",
                content: content
            })
        });

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(`Error adding message: ${response.status} - ${errorData.message || JSON.stringify(errorData)}`);
        }

        return await response.json();
    }

    // Create a run
    async createRun(threadId) {
        const response = await fetch(`${this.baseURL}/threads/${threadId}/runs`, {
            method: "POST",
            headers: {
                "Authorization": `Bearer ${this.apiKey}`,
                "OpenAI-Beta": "assistants=v2",
                "Content-Type": "application/json"
            },
            body: JSON.stringify({
                assistant_id: this.assistantId
            })
        });

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(`Error creating run: ${response.status} - ${errorData.message || JSON.stringify(errorData)}`);
        }

        return await response.json();
    }

    // Get run status
    async getRunStatus(threadId, runId) {
        const response = await fetch(`${this.baseURL}/threads/${threadId}/runs/${runId}`, {
            method: "GET",
            headers: {
                "Authorization": `Bearer ${this.apiKey}`,
                "OpenAI-Beta": "assistants=v2",
            }
        });

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(`Error getting run status: ${response.status} - ${errorData.message || JSON.stringify(errorData)}`);
        }

        return await response.json();
    }

    // Get messages from thread
    async getMessages(threadId) {
        const response = await fetch(`${this.baseURL}/threads/${threadId}/messages`, {
            method: "GET",
            headers: {
                "Authorization": `Bearer ${this.apiKey}`,
                "OpenAI-Beta": "assistants=v2",
            }
        });

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(`Error retrieving messages: ${response.status} - ${errorData.message || JSON.stringify(errorData)}`);
        }

        return await response.json();
    }

    // Poll run until completion
    async pollRunCompletion(threadId, runId, onProgress) {
        let attempts = 0;
        const maxAttempts = CONFIG.MAX_POLLING_ATTEMPTS;

        while (attempts < maxAttempts) {
            const runData = await this.getRunStatus(threadId, runId);
            const status = runData.status;

            // Update progress
            if (onProgress) {
                const progress = Math.min((attempts / maxAttempts) * 100, 95);
                onProgress(progress, status);
            }

            if (status === 'completed') {
                return runData;
            }

            if (status === 'failed' || status === 'cancelled' || status === 'expired') {
                throw new Error(`Run failed with status: ${status}. Error: ${JSON.stringify(runData.last_error)}`);
            }

            await new Promise(resolve => setTimeout(resolve, CONFIG.POLLING_INTERVAL));
            attempts++;
        }

        throw new Error('Run polling timeout exceeded');
    }

    // Extract workflow JSON from assistant response
    extractWorkflowJSON(messageText) {
        const jsonMatch = messageText.match(/```json\n([\s\S]*?)\n```/);
        if (jsonMatch && jsonMatch[1]) {
            try {
                return JSON.parse(jsonMatch[1]);
            } catch (error) {
                throw new Error('Invalid JSON in assistant response');
            }
        }
        throw new Error('No JSON workflow found in assistant response');
    }

    // Main workflow generation method
    async generateWorkflow(prompt, onProgress) {
        try {
            // Create thread
            if (onProgress) onProgress(10, 'Creating conversation thread...');
            const threadData = await this.createThread();
            const threadId = threadData.id;

            // Add message
            if (onProgress) onProgress(20, 'Sending your request...');
            await this.addMessage(threadId, prompt);

            // Create run
            if (onProgress) onProgress(30, 'Starting AI processing...');
            const runData = await this.createRun(threadId);
            const runId = runData.id;

            // Poll for completion
            await this.pollRunCompletion(threadId, runId, (progress, status) => {
                if (onProgress) onProgress(30 + (progress * 0.6), `Processing: ${status}`);
            });

            // Get messages
            if (onProgress) onProgress(95, 'Retrieving generated workflow...');
            const messagesData = await this.getMessages(threadId);

            // Find assistant response
            const assistantMessage = messagesData.data.find(msg => msg.run_id === runId && msg.role === 'assistant');
            if (!assistantMessage || !assistantMessage.content || assistantMessage.content.length === 0) {
                throw new Error("No response found from the assistant.");
            }

            const textContent = assistantMessage.content.find(content => content.type === 'text');
            if (!textContent) {
                throw new Error("Assistant response does not contain text content.");
            }

            // Extract and return workflow JSON
            if (onProgress) onProgress(100, 'Workflow generated successfully!');
            return this.extractWorkflowJSON(textContent.text.value);

        } catch (error) {
            console.error('API Error:', error);
            throw error;
        }
    }
}
