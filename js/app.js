// Main Application - Orchestrates all modules
import { APIService } from './api.js';
import { UIController } from './ui.js';
import { CONFIG } from './config.js';

class GEN8NApp {
    constructor() {
        this.apiService = new APIService();
        this.uiController = new UIController();
        this.isInitialized = false;
    }

    // Initialize the application
    async init() {
        try {
            console.log('🚀 Initializing GEN8N App...');
            
            // Wait for DOM to be ready
            if (document.readyState === 'loading') {
                await new Promise(resolve => {
                    document.addEventListener('DOMContentLoaded', resolve);
                });
            }

            // Initialize UI
            this.uiController.init();
            
            // Setup event listeners
            this.setupEventListeners();
            
            // Add CSS animations
            this.addAnimations();
            
            this.isInitialized = true;
            console.log('✅ GEN8N App initialized successfully');
            
        } catch (error) {
            console.error('❌ Failed to initialize app:', error);
            this.handleError(error);
        }
    }

    setupEventListeners() {
        // Listen for workflow generation requests
        document.addEventListener('generateWorkflow', (event) => {
            this.handleWorkflowGeneration(event.detail.prompt);
        });

        // Handle keyboard shortcuts
        document.addEventListener('keydown', (event) => {
            // Ctrl/Cmd + Enter to generate
            if ((event.ctrlKey || event.metaKey) && event.key === 'Enter') {
                event.preventDefault();
                this.uiController.onGenerateClick();
            }
            
            // Escape to close loading
            if (event.key === 'Escape' && this.uiController.isGenerating) {
                this.cancelGeneration();
            }
        });

        // Handle window resize for responsive adjustments
        window.addEventListener('resize', this.debounce(() => {
            this.handleResize();
        }, 250));

        // Handle visibility change to pause/resume
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.handlePageHidden();
            } else {
                this.handlePageVisible();
            }
        });
    }

    // Main workflow generation handler
    async handleWorkflowGeneration(prompt) {
        try {
            console.log('🎯 Starting workflow generation for:', prompt);
            
            // Show loading state
            this.uiController.showLoading();
            
            // Generate workflow with progress updates
            const workflowJson = await this.apiService.generateWorkflow(
                prompt,
                (progress, message) => {
                    this.uiController.updateProgress(progress, message);
                }
            );
            
            // Display the result
            this.uiController.displayWorkflow(workflowJson);
            
            // Update usage tracking
            this.uiController.incrementUsage();
            
            // Show success message
            this.uiController.showToast('Workflow generated successfully! 🎉');
            
            // Track analytics (if implemented)
            this.trackEvent('workflow_generated', {
                prompt_length: prompt.length,
                success: true
            });
            
        } catch (error) {
            console.error('❌ Workflow generation failed:', error);
            this.uiController.displayError(error);
            this.uiController.showToast('Failed to generate workflow', 'error');
            
            // Track error analytics
            this.trackEvent('workflow_generation_failed', {
                error: error.message,
                prompt_length: prompt.length
            });
            
        } finally {
            this.uiController.hideLoading();
        }
    }

    // Cancel ongoing generation
    cancelGeneration() {
        console.log('🛑 Cancelling workflow generation');
        this.uiController.hideLoading();
        this.uiController.showToast('Generation cancelled', 'error');
    }

    // Handle application errors
    handleError(error) {
        console.error('🚨 Application error:', error);
        
        // Show user-friendly error message
        const errorMessage = this.getUserFriendlyErrorMessage(error);
        this.uiController.showToast(errorMessage, 'error');
        
        // Track error for debugging
        this.trackEvent('app_error', {
            error: error.message,
            stack: error.stack
        });
    }

    // Convert technical errors to user-friendly messages
    getUserFriendlyErrorMessage(error) {
        const message = error.message.toLowerCase();
        
        if (message.includes('network') || message.includes('fetch')) {
            return 'Network connection issue. Please check your internet connection.';
        }
        
        if (message.includes('api key') || message.includes('unauthorized')) {
            return 'API authentication issue. Please contact support.';
        }
        
        if (message.includes('rate limit') || message.includes('quota')) {
            return 'Service temporarily unavailable. Please try again later.';
        }
        
        if (message.includes('timeout')) {
            return 'Request timed out. Please try again.';
        }
        
        return 'An unexpected error occurred. Please try again.';
    }

    // Handle window resize
    handleResize() {
        // Adjust mobile navigation if needed
        if (window.innerWidth > 768) {
            const navMenu = document.querySelector('.nav-menu');
            navMenu?.classList.remove('active');
        }
        
        // Trigger custom resize event for components
        document.dispatchEvent(new CustomEvent('appResize', {
            detail: {
                width: window.innerWidth,
                height: window.innerHeight
            }
        }));
    }

    // Handle page visibility changes
    handlePageHidden() {
        console.log('📱 Page hidden - pausing activities');
        // Pause any ongoing animations or polling
    }

    handlePageVisible() {
        console.log('👀 Page visible - resuming activities');
        // Resume activities if needed
    }

    // Add CSS animations dynamically
    addAnimations() {
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            
            @keyframes slideOut {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
            
            @keyframes pulse {
                0%, 100% { opacity: 1; }
                50% { opacity: 0.5; }
            }
            
            .loading-pulse {
                animation: pulse 2s infinite;
            }
        `;
        document.head.appendChild(style);
    }

    // Utility: Debounce function
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    // Analytics tracking (placeholder)
    trackEvent(eventName, properties = {}) {
        // Implement analytics tracking here (Google Analytics, Mixpanel, etc.)
        console.log('📊 Event tracked:', eventName, properties);
        
        // Example: Send to analytics service
        // analytics.track(eventName, properties);
    }

    // Health check
    async healthCheck() {
        try {
            // Check if all services are available
            const checks = {
                ui: this.uiController.isInitialized,
                api: !!this.apiService,
                localStorage: this.checkLocalStorage(),
                network: await this.checkNetwork()
            };
            
            console.log('🏥 Health check:', checks);
            return checks;
            
        } catch (error) {
            console.error('❌ Health check failed:', error);
            return { error: error.message };
        }
    }

    checkLocalStorage() {
        try {
            localStorage.setItem('test', 'test');
            localStorage.removeItem('test');
            return true;
        } catch {
            return false;
        }
    }

    async checkNetwork() {
        try {
            const response = await fetch('https://api.github.com/zen', { 
                method: 'HEAD',
                mode: 'no-cors'
            });
            return true;
        } catch {
            return false;
        }
    }

    // Public API for external access
    getAPI() {
        return {
            generateWorkflow: (prompt) => this.handleWorkflowGeneration(prompt),
            getUsage: () => this.uiController.getUsageCount(),
            setTheme: (theme) => this.uiController.setTheme(theme),
            healthCheck: () => this.healthCheck()
        };
    }
}

// Initialize and expose the app
const app = new GEN8NApp();

// Auto-initialize when script loads
app.init().catch(console.error);

// Expose app globally for debugging
window.GEN8N = app.getAPI();

export default app;
