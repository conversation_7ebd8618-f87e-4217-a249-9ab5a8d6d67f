// UI Controller - DOM Manipulation and User Interface
import { CONFIG, TEMPLATES } from './config.js';
import { TEMPLATE_CATEGORIES, TemplateManager } from './templates.js';

export class UIController {
    constructor() {
        this.elements = this.initializeElements();
        this.currentTab = 'visual';
        this.isGenerating = false;
    }

    initializeElements() {
        return {
            // Navigation
            themeToggle: document.getElementById('themeToggle'),
            navToggle: document.getElementById('navToggle'),
            navMenu: document.querySelector('.nav-menu'),

            // Generator
            promptInput: document.getElementById('promptInput'),
            generateBtn: document.getElementById('generateBtn'),
            clearBtn: document.getElementById('clearBtn'),
            promptTemplates: document.getElementById('promptTemplates'),
            randomTemplateBtn: document.getElementById('randomTemplateBtn'),
            templateCount: document.getElementById('templateCount'),

            // Output
            tabBtns: document.querySelectorAll('.tab-btn'),
            tabContents: document.querySelectorAll('.tab-content'),
            jsonOutput: document.getElementById('jsonOutput'),
            n8nDemo: document.getElementById('n8n-demo-component-display'),
            copyJsonBtn: document.getElementById('copyJsonBtn'),

            // Loading
            loadingOverlay: document.getElementById('loadingOverlay'),
            loadingText: document.getElementById('loadingText'),
            progressFill: document.getElementById('progressFill'),
            progressText: document.getElementById('progressText'),

            // Usage
            usageFill: document.querySelector('.usage-fill')
        };
    }

    // Initialize UI components
    init() {
        this.setupEventListeners();
        this.renderTemplates();
        this.initializeTheme();
        this.updateUsage();
    }

    setupEventListeners() {
        // Theme toggle
        this.elements.themeToggle?.addEventListener('click', () => this.toggleTheme());

        // Mobile navigation
        this.elements.navToggle?.addEventListener('click', () => this.toggleMobileNav());

        // Generator buttons
        this.elements.generateBtn?.addEventListener('click', () => this.onGenerateClick());
        this.elements.clearBtn?.addEventListener('click', () => this.clearInput());

        // Tab switching
        this.elements.tabBtns.forEach(btn => {
            btn.addEventListener('click', () => this.switchTab(btn.dataset.tab));
        });

        // Copy JSON
        this.elements.copyJsonBtn?.addEventListener('click', () => this.copyJSON());

        // Random template
        this.elements.randomTemplateBtn?.addEventListener('click', () => this.useRandomTemplate());

        // Smooth scrolling for CTA button
        window.scrollToGenerator = () => {
            document.getElementById('generator')?.scrollIntoView({ behavior: 'smooth' });
        };
    }

    renderTemplates() {
        if (!this.elements.promptTemplates) return;

        this.elements.promptTemplates.innerHTML = '';

        // Create search input
        const searchContainer = document.createElement('div');
        searchContainer.className = 'template-search';
        searchContainer.innerHTML = `
            <input type="text" placeholder="Search templates..." class="template-search-input" id="templateSearch">
            <i class="fas fa-search"></i>
        `;
        this.elements.promptTemplates.appendChild(searchContainer);

        // Create category tabs
        const categoriesContainer = document.createElement('div');
        categoriesContainer.className = 'template-categories';

        const categories = ['All', ...TemplateManager.getAllCategories()];
        categories.forEach((category, index) => {
            const tab = document.createElement('button');
            tab.className = `category-tab ${index === 0 ? 'active' : ''}`;
            tab.textContent = category;
            tab.addEventListener('click', () => this.filterTemplatesByCategory(category));
            categoriesContainer.appendChild(tab);
        });
        this.elements.promptTemplates.appendChild(categoriesContainer);

        // Create templates container
        const templatesContainer = document.createElement('div');
        templatesContainer.className = 'templates-container';
        templatesContainer.id = 'templatesContainer';
        this.elements.promptTemplates.appendChild(templatesContainer);

        // Setup search functionality
        const searchInput = document.getElementById('templateSearch');
        searchInput.addEventListener('input', (e) => this.searchTemplates(e.target.value));

        // Render all templates initially
        this.renderTemplateCards('All');

        // Update template count
        this.updateTemplateCount();
    }

    filterTemplatesByCategory(category) {
        // Update active tab
        document.querySelectorAll('.category-tab').forEach(tab => {
            tab.classList.toggle('active', tab.textContent === category);
        });

        // Render templates for category
        this.renderTemplateCards(category);
    }

    renderTemplateCards(category) {
        const container = document.getElementById('templatesContainer');
        if (!container) return;

        container.innerHTML = '';

        let templatesToShow = [];
        if (category === 'All') {
            // Show all templates from all categories
            Object.entries(TEMPLATE_CATEGORIES).forEach(([cat, templates]) => {
                templates.forEach(template => {
                    templatesToShow.push({ ...template, category: cat });
                });
            });
        } else {
            // Show templates from specific category
            const categoryTemplates = TemplateManager.getTemplatesByCategory(category);
            templatesToShow = categoryTemplates.map(template => ({ ...template, category }));
        }

        templatesToShow.forEach(template => {
            const card = this.createTemplateCard(template);
            container.appendChild(card);
        });
    }

    createTemplateCard(template) {
        const card = document.createElement('div');
        card.className = 'template-card';
        card.innerHTML = `
            <div class="template-header">
                <h4 class="template-title">${template.title}</h4>
                <span class="template-category">${template.category}</span>
            </div>
            <p class="template-description">${template.description}</p>
            <div class="template-actions">
                <button class="btn btn-small btn-secondary template-preview-btn">
                    <i class="fas fa-eye"></i> Preview
                </button>
                <button class="btn btn-small btn-primary template-use-btn">
                    <i class="fas fa-plus"></i> Use Template
                </button>
            </div>
        `;

        // Add event listeners
        const previewBtn = card.querySelector('.template-preview-btn');
        const useBtn = card.querySelector('.template-use-btn');

        previewBtn.addEventListener('click', () => this.previewTemplate(template));
        useBtn.addEventListener('click', () => this.useTemplate(template));

        return card;
    }

    searchTemplates(query) {
        if (!query.trim()) {
            this.renderTemplateCards('All');
            return;
        }

        const results = TemplateManager.searchTemplates(query);
        const container = document.getElementById('templatesContainer');
        if (!container) return;

        container.innerHTML = '';

        if (results.length === 0) {
            container.innerHTML = '<div class="no-results">No templates found matching your search.</div>';
            return;
        }

        results.forEach(template => {
            const card = this.createTemplateCard(template);
            container.appendChild(card);
        });
    }

    previewTemplate(template) {
        // Create modal for template preview
        const modal = document.createElement('div');
        modal.className = 'template-modal';
        modal.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h3>${template.title}</h3>
                    <button class="modal-close">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="template-meta">
                        <span class="template-category-badge">${template.category}</span>
                    </div>
                    <p class="template-full-description">${template.description}</p>
                    <div class="template-prompt-preview">
                        <h4>Full Prompt:</h4>
                        <pre>${template.prompt}</pre>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary modal-cancel">Cancel</button>
                    <button class="btn btn-primary modal-use">Use This Template</button>
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        // Add event listeners
        modal.querySelector('.modal-close').addEventListener('click', () => modal.remove());
        modal.querySelector('.modal-cancel').addEventListener('click', () => modal.remove());
        modal.querySelector('.modal-use').addEventListener('click', () => {
            this.useTemplate(template);
            modal.remove();
        });

        // Close on backdrop click
        modal.addEventListener('click', (e) => {
            if (e.target === modal) modal.remove();
        });
    }

    useTemplate(template) {
        this.elements.promptInput.value = template.prompt;
        this.elements.promptInput.focus();
        this.showToast(`Template "${template.title}" loaded!`);

        // Scroll to input
        this.elements.promptInput.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }

    useRandomTemplate() {
        const randomTemplate = TemplateManager.getRandomTemplate();
        this.useTemplate(randomTemplate);
    }

    updateTemplateCount() {
        const totalTemplates = Object.values(TEMPLATE_CATEGORIES).flat().length;
        if (this.elements.templateCount) {
            this.elements.templateCount.textContent = `${totalTemplates} enterprise templates available`;
        }
    }

    // Theme management
    initializeTheme() {
        const savedTheme = localStorage.getItem('gen8n-theme') || 'light';
        this.setTheme(savedTheme);
    }

    toggleTheme() {
        const currentTheme = document.documentElement.getAttribute('data-theme') || 'light';
        const newTheme = currentTheme === 'light' ? 'dark' : 'light';
        this.setTheme(newTheme);
    }

    setTheme(theme) {
        document.documentElement.setAttribute('data-theme', theme);
        localStorage.setItem('gen8n-theme', theme);

        if (this.elements.themeToggle) {
            const icon = this.elements.themeToggle.querySelector('i');
            icon.className = theme === 'light' ? 'fas fa-moon' : 'fas fa-sun';
        }
    }

    // Mobile navigation
    toggleMobileNav() {
        this.elements.navMenu?.classList.toggle('active');
    }

    // Tab management
    switchTab(tabName) {
        this.currentTab = tabName;

        // Update tab buttons
        this.elements.tabBtns.forEach(btn => {
            btn.classList.toggle('active', btn.dataset.tab === tabName);
        });

        // Update tab content
        this.elements.tabContents.forEach(content => {
            content.classList.toggle('active', content.id === `${tabName}-tab`);
        });
    }

    // Input management
    clearInput() {
        this.elements.promptInput.value = '';
        this.elements.promptInput.focus();
    }

    getPromptValue() {
        return this.elements.promptInput.value.trim();
    }

    // Loading states
    showLoading() {
        this.isGenerating = true;
        this.elements.loadingOverlay.style.display = 'flex';
        this.elements.generateBtn.disabled = true;
        this.elements.generateBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Generating...';
        this.updateProgress(0, 'Initializing...');
    }

    hideLoading() {
        this.isGenerating = false;
        this.elements.loadingOverlay.style.display = 'none';
        this.elements.generateBtn.disabled = false;
        this.elements.generateBtn.innerHTML = '<i class="fas fa-magic"></i> Generate Workflow';
    }

    updateProgress(percentage, message) {
        if (this.elements.progressFill) {
            this.elements.progressFill.style.width = `${percentage}%`;
        }
        if (this.elements.progressText) {
            this.elements.progressText.textContent = `${Math.round(percentage)}%`;
        }
        if (this.elements.loadingText) {
            this.elements.loadingText.textContent = message;
        }
    }

    // Output display
    displayWorkflow(workflowJson) {
        // Display JSON
        this.elements.jsonOutput.textContent = JSON.stringify(workflowJson, null, 2);

        // Update n8n demo component
        this.updateN8nDemo(workflowJson);

        // Switch to visual tab
        this.switchTab('visual');
    }

    updateN8nDemo(workflowJson) {
        if (!this.elements.n8nDemo) return;

        // Clear existing content
        this.elements.n8nDemo.innerHTML = '';

        // Create n8n demo component
        const n8nDemoComponent = document.createElement('n8n-demo');
        n8nDemoComponent.setAttribute('workflow', JSON.stringify(workflowJson));
        n8nDemoComponent.setAttribute('collapseformobile', 'false');
        n8nDemoComponent.setAttribute('clicktointeract', 'true');
        n8nDemoComponent.setAttribute('hidecanvaserrors', 'true');
        n8nDemoComponent.setAttribute('disableinteractivity', 'false');
        n8nDemoComponent.setAttribute('theme', 'light');

        this.elements.n8nDemo.appendChild(n8nDemoComponent);
    }

    displayError(error) {
        this.elements.jsonOutput.textContent = `Error: ${error.message}`;
        this.switchTab('json');
    }

    // Copy functionality
    async copyJSON() {
        try {
            await navigator.clipboard.writeText(this.elements.jsonOutput.textContent);
            this.showToast('JSON copied to clipboard!');
        } catch (error) {
            console.error('Failed to copy:', error);
            this.showToast('Failed to copy JSON', 'error');
        }
    }

    // Toast notifications
    showToast(message, type = 'success') {
        const toast = document.createElement('div');
        toast.className = `toast toast-${type}`;
        toast.textContent = message;
        toast.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${type === 'success' ? 'var(--secondary-color)' : 'var(--danger-color)'};
            color: white;
            padding: 1rem 1.5rem;
            border-radius: var(--radius-md);
            z-index: 10000;
            animation: slideIn 0.3s ease;
        `;

        document.body.appendChild(toast);
        setTimeout(() => {
            toast.style.animation = 'slideOut 0.3s ease';
            setTimeout(() => toast.remove(), 300);
        }, 3000);
    }

    // Usage tracking
    updateUsage() {
        const usage = this.getUsageCount();
        const percentage = (usage / CONFIG.MAX_DAILY_GENERATIONS) * 100;

        if (this.elements.usageFill) {
            this.elements.usageFill.style.width = `${percentage}%`;
        }

        const usageText = document.querySelector('.usage-text');
        if (usageText) {
            usageText.textContent = `${usage}/${CONFIG.MAX_DAILY_GENERATIONS} generations used today`;
        }
    }

    getUsageCount() {
        const today = new Date().toDateString();
        const usage = JSON.parse(localStorage.getItem('gen8n-usage') || '{}');
        return usage[today] || 0;
    }

    incrementUsage() {
        const today = new Date().toDateString();
        const usage = JSON.parse(localStorage.getItem('gen8n-usage') || '{}');
        usage[today] = (usage[today] || 0) + 1;
        localStorage.setItem('gen8n-usage', JSON.stringify(usage));
        this.updateUsage();
    }

    canGenerate() {
        return this.getUsageCount() < CONFIG.MAX_DAILY_GENERATIONS;
    }

    // Event handlers
    onGenerateClick() {
        const prompt = this.getPromptValue();
        if (!prompt) {
            this.showToast('Please enter a workflow description', 'error');
            return;
        }

        if (!this.canGenerate()) {
            this.showToast('Daily generation limit reached', 'error');
            return;
        }

        // Dispatch custom event for generation
        document.dispatchEvent(new CustomEvent('generateWorkflow', { detail: { prompt } }));
    }
}
