// Configuration - API Keys and Settings
export const CONFIG = {
    // OpenAI Configuration
    OPENAI_API_KEY: '********************************************************************************************************************************************************************',
    OPENAI_API_BASE_URL: 'https://api.openai.com/v1',
    ASSISTANT_ID: 'asst_EVjHFlXIaDM1E3xHOqzJxsXs',

    // App Settings
    MAX_DAILY_GENERATIONS: 10,
    POLLING_INTERVAL: 1000,
    MAX_POLLING_ATTEMPTS: 60,

    // UI Settings
    ANIMATION_DURATION: 300,
    LOADING_MESSAGES: [
        'Analyzing your requirements...',
        'Generating workflow structure...',
        'Optimizing node connections...',
        'Finalizing your automation...'
    ]
};

// Workflow Templates - Complex Real-World Scenarios
export const TEMPLATES = [
    // AI Agent Workflows
    "Create an AI customer support agent that monitors support tickets in Zendesk, analyzes sentiment using OpenAI, categorizes issues, auto-responds to simple queries, escalates complex issues to human agents, and tracks resolution metrics in a dashboard",

    "Build an AI content creation pipeline that monitors trending topics from Twitter API, generates blog post outlines using GPT-4, creates SEO-optimized content, generates social media posts, schedules publication across WordPress and social platforms, and tracks engagement metrics",

    "Design an AI sales assistant that monitors new leads from multiple sources (website forms, LinkedIn, cold emails), enriches lead data using Clearbit API, scores leads with AI analysis, personalizes outreach messages using GPT-4, schedules follow-ups, and updates CRM with interaction history",

    "Develop an AI-powered recruitment workflow that scrapes job applications from multiple platforms, uses AI to screen resumes against job requirements, conducts initial video interview analysis, ranks candidates, sends personalized rejection/acceptance emails, and schedules interviews with hiring managers",

    "Create an AI financial analyst agent that monitors market data APIs, analyzes news sentiment using NLP, generates investment recommendations, creates automated trading signals, sends risk alerts, and produces daily market analysis reports for stakeholders",

    // Complex Business Automation
    "Build a comprehensive e-commerce order fulfillment system that processes orders from Shopify, validates inventory across multiple warehouses, calculates optimal shipping routes, generates shipping labels, sends tracking notifications, handles returns processing, and updates financial records in QuickBooks",

    "Design a multi-tenant SaaS customer onboarding workflow that triggers on new Stripe subscriptions, creates user accounts in Auth0, provisions resources in AWS, sends welcome email sequences, schedules onboarding calls, tracks feature adoption, and handles trial-to-paid conversions",

    "Create an advanced lead nurturing system that captures leads from multiple sources (website, ads, events), scores leads using behavioral data and AI, segments audiences dynamically, triggers personalized email sequences, schedules sales calls, tracks conversion funnels, and optimizes campaigns based on performance",

    "Build a comprehensive project management automation that monitors GitHub commits, runs automated tests, deploys to staging environments, notifies stakeholders, creates JIRA tickets for bugs, generates release notes, schedules production deployments, and tracks deployment success metrics",

    "Design a sophisticated customer churn prevention system that analyzes user behavior patterns, identifies at-risk customers using ML models, triggers personalized retention campaigns, schedules intervention calls, offers targeted discounts, and measures retention success rates",

    // Advanced Data Processing
    "Create a real-time data pipeline that ingests streaming data from IoT sensors, processes data using Apache Kafka, applies ML models for anomaly detection, triggers alerts for critical issues, stores processed data in time-series databases, generates predictive maintenance schedules, and creates executive dashboards",

    "Build a comprehensive social media intelligence system that monitors brand mentions across platforms, analyzes sentiment and trends, identifies influencers and key conversations, generates competitive analysis reports, triggers crisis management protocols, and provides real-time social media ROI metrics",

    "Design an advanced inventory management system that monitors stock levels across multiple locations, predicts demand using historical data and ML, automatically reorders products, optimizes supplier selection, tracks delivery performance, handles quality control workflows, and generates procurement analytics",

    "Create a sophisticated fraud detection workflow that monitors transactions in real-time, applies ML models for risk scoring, cross-references with blacklists and external APIs, triggers manual review for suspicious activities, automatically blocks high-risk transactions, and generates compliance reports",

    "Build an intelligent document processing system that extracts data from various document types (PDFs, images, emails), uses OCR and NLP for content analysis, validates extracted data against business rules, routes documents for approval workflows, stores processed data in appropriate systems, and tracks processing metrics",

    // Industry-Specific Solutions
    "Design a healthcare patient management workflow that monitors patient data from multiple sources, schedules appointments based on urgency and availability, sends automated reminders, processes insurance claims, tracks treatment outcomes, ensures HIPAA compliance, and generates clinical reports",

    "Create a real estate lead management system that captures leads from multiple listing platforms, enriches contact data, qualifies leads using AI scoring, assigns to appropriate agents, schedules property viewings, tracks showing feedback, manages offer negotiations, and analyzes market trends",

    "Build a comprehensive educational platform automation that enrolls students from applications, creates personalized learning paths, tracks progress and engagement, identifies at-risk students, triggers intervention workflows, generates progress reports for parents, and optimizes curriculum based on performance data",

    "Design a restaurant chain operations workflow that monitors sales data across locations, manages inventory and supply chain, schedules staff based on predicted demand, tracks food safety compliance, handles customer feedback and reviews, optimizes menu pricing, and generates operational analytics",

    "Create a manufacturing quality control system that monitors production line data, detects defects using computer vision, triggers quality alerts, manages supplier performance, tracks compliance with industry standards, schedules maintenance based on equipment data, and generates quality reports"
];

// Theme Configuration
export const THEME_CONFIG = {
    STORAGE_KEY: 'gen8n-theme',
    DEFAULT_THEME: 'light',
    THEMES: {
        light: {
            name: 'Light',
            icon: 'fas fa-sun'
        },
        dark: {
            name: 'Dark',
            icon: 'fas fa-moon'
        }
    }
};
