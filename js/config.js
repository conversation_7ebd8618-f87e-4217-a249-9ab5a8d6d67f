// Configuration - API Keys and Settings
export const CONFIG = {
    // OpenAI Configuration
    OPENAI_API_KEY: '********************************************************************************************************************************************************************',
    OPENAI_API_BASE_URL: 'https://api.openai.com/v1',
    ASSISTANT_ID: 'asst_EVjHFlXIaDM1E3xHOqzJxsXs',
    
    // App Settings
    MAX_DAILY_GENERATIONS: 10,
    POLLING_INTERVAL: 1000,
    MAX_POLLING_ATTEMPTS: 60,
    
    // UI Settings
    ANIMATION_DURATION: 300,
    LOADING_MESSAGES: [
        'Analyzing your requirements...',
        'Generating workflow structure...',
        'Optimizing node connections...',
        'Finalizing your automation...'
    ]
};

// Workflow Templates
export const TEMPLATES = [
    "Create a workflow that reads new Gmail emails and saves them to Google Sheets",
    "Monitor FTP folder and send Discord notification when new file is added",
    "Schedule daily task to fetch API data and store in Airtable",
    "Get Twitter user tweets and save to MongoDB database",
    "Monitor RSS feed and send daily email summary",
    "Auto-publish WordPress blog to social media platforms",
    "Process IoT sensor data with real-time analysis and alerts",
    "Automate invoice generation with payment integration",
    "Monitor brand mentions on social media with daily Slack reports",
    "Sync CRM contacts with email marketing platform",
    "Create project tasks from emails and Discord messages",
    "Extract e-commerce data, enrich and import to database",
    "Auto-publish blog content from Google Sheets with scheduling"
];

// Theme Configuration
export const THEME_CONFIG = {
    STORAGE_KEY: 'gen8n-theme',
    DEFAULT_THEME: 'light',
    THEMES: {
        light: {
            name: 'Light',
            icon: 'fas fa-sun'
        },
        dark: {
            name: 'Dark',
            icon: 'fas fa-moon'
        }
    }
};
