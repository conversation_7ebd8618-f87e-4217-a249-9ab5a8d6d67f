/* Responsive Design - Mobile First */

/* Tablet Styles */
@media (max-width: 1024px) {
    .hero-container {
        grid-template-columns: 1fr;
        text-align: center;
        gap: var(--spacing-xl);
    }
    
    .hero-title {
        font-size: 3rem;
    }
    
    .generator-container {
        grid-template-columns: 1fr;
        gap: var(--spacing-xl);
    }
    
    .features-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }
}

/* Mobile Styles */
@media (max-width: 768px) {
    .container {
        padding: 0 var(--spacing-md);
    }
    
    .nav-container {
        padding: 0 var(--spacing-md);
    }
    
    .nav-menu {
        display: none;
        position: absolute;
        top: 100%;
        left: 0;
        width: 100%;
        background: var(--bg-primary);
        border-top: 1px solid var(--border-color);
        flex-direction: column;
        padding: var(--spacing-lg);
        gap: var(--spacing-md);
    }
    
    .nav-menu.active {
        display: flex;
    }
    
    .nav-toggle {
        display: flex;
    }
    
    .hero {
        min-height: 60vh;
        padding: var(--spacing-xl) 0;
    }
    
    .hero-title {
        font-size: 2.5rem;
    }
    
    .hero-subtitle {
        font-size: 1.125rem;
    }
    
    .hero-cta {
        flex-direction: column;
        align-items: center;
        gap: var(--spacing-md);
    }
    
    .hero-stats {
        justify-content: center;
        gap: var(--spacing-lg);
    }
    
    .features-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }
    
    .templates-grid {
        grid-template-columns: 1fr;
    }
    
    .input-actions {
        flex-direction: column;
    }
    
    .output-tabs {
        overflow-x: auto;
        white-space: nowrap;
    }
    
    .tab-btn {
        flex-shrink: 0;
    }
    
    .export-buttons {
        gap: var(--spacing-sm);
    }
}

/* Small Mobile */
@media (max-width: 480px) {
    .hero-title {
        font-size: 2rem;
    }
    
    .hero-subtitle {
        font-size: 1rem;
    }
    
    .hero-stats {
        flex-direction: column;
        gap: var(--spacing-md);
    }
    
    .generator-input, .generator-output {
        padding: var(--spacing-lg);
    }
    
    .input-header {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-sm);
    }
    
    .usage-indicator {
        align-items: flex-start;
    }
    
    .n8n-demo-container {
        height: 300px;
    }
    
    .json-container {
        height: 300px;
    }
}

/* Large Screens */
@media (min-width: 1440px) {
    .container {
        max-width: 1400px;
    }
    
    .nav-container {
        max-width: 1400px;
    }
    
    .hero-container {
        max-width: 1400px;
    }
    
    .hero-title {
        font-size: 4rem;
    }
}

/* Print Styles */
@media print {
    .navbar,
    .hero-visual,
    .loading-overlay {
        display: none;
    }
    
    .hero-container {
        grid-template-columns: 1fr;
    }
    
    .generator-container {
        grid-template-columns: 1fr;
    }
    
    body {
        background: white;
        color: black;
    }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
    
    .node {
        animation: none;
    }
}

/* High Contrast */
@media (prefers-contrast: high) {
    :root {
        --border-color: #000000;
        --text-secondary: #000000;
    }
    
    [data-theme="dark"] {
        --border-color: #ffffff;
        --text-secondary: #ffffff;
    }
}
