/* Layout - Navigation, Hero, Sections */

/* Navigation */
.navbar {
    background: var(--bg-primary);
    border-bottom: 1px solid var(--border-color);
    position: sticky;
    top: 0;
    z-index: 1000;
    backdrop-filter: blur(10px);
    box-shadow: var(--shadow-sm);
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-lg);
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 70px;
}

.nav-brand {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
    text-decoration: none;
}

.nav-brand i {
    font-size: 1.75rem;
}

.nav-menu {
    display: flex;
    align-items: center;
    gap: var(--spacing-xl);
}

.nav-link {
    color: var(--text-secondary);
    text-decoration: none;
    font-weight: 500;
    transition: color var(--transition-fast);
}

.nav-link:hover {
    color: var(--primary-color);
}

.nav-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
    gap: 4px;
}

.nav-toggle span {
    width: 25px;
    height: 3px;
    background: var(--text-primary);
    transition: var(--transition-fast);
}

/* Hero Section */
.hero {
    background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
    padding: var(--spacing-2xl) 0;
    min-height: 80vh;
    display: flex;
    align-items: center;
}

.hero-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-lg);
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-2xl);
    align-items: center;
}

.hero-content {
    max-width: 600px;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    line-height: 1.1;
    margin-bottom: var(--spacing-lg);
    color: var(--text-primary);
}

.hero-subtitle {
    font-size: 1.25rem;
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xl);
    line-height: 1.6;
}

.hero-cta {
    display: flex;
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-2xl);
}

.hero-stats {
    display: flex;
    gap: var(--spacing-xl);
}

.stat {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary-color);
}

.stat-label {
    font-size: 0.875rem;
    color: var(--text-muted);
}

/* Hero Visual */
.hero-visual {
    display: flex;
    justify-content: center;
    align-items: center;
}

.workflow-preview {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
    align-items: center;
}

.node {
    background: var(--bg-card);
    border: 2px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-sm);
    min-width: 120px;
    box-shadow: var(--shadow-md);
    transition: all var(--transition-normal);
    animation: float 3s ease-in-out infinite;
}

.node:nth-child(2) { animation-delay: 1s; }
.node:nth-child(4) { animation-delay: 2s; }

.node i {
    font-size: 1.5rem;
    color: var(--primary-color);
}

.node span {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-primary);
}

.connection {
    width: 2px;
    height: 30px;
    background: linear-gradient(to bottom, var(--primary-color), var(--secondary-color));
    border-radius: 1px;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

/* Features Section */
.features {
    padding: var(--spacing-2xl) 0;
    background: var(--bg-primary);
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-xl);
}

/* Generator Section */
.generator {
    padding: var(--spacing-2xl) 0;
    background: var(--bg-secondary);
}

.generator-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-2xl);
    margin-top: var(--spacing-xl);
}

.generator-input, .generator-output {
    background: var(--bg-card);
    border-radius: var(--radius-lg);
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--border-color);
}

.input-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
}

.input-header h3 {
    color: var(--text-primary);
    margin: 0;
}

.prompt-templates h4 {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
}

.templates-subtitle {
    color: var(--text-muted);
    font-size: 0.875rem;
    margin-bottom: var(--spacing-md);
    line-height: 1.5;
}

.template-actions-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
    padding: var(--spacing-sm) 0;
}

.template-count {
    color: var(--text-muted);
    font-size: 0.875rem;
    font-weight: 500;
}

.templates-grid {
    /* This will be replaced by the new template system */
    margin-bottom: var(--spacing-lg);
}

.prompt-input-container {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.input-actions {
    display: flex;
    gap: var(--spacing-md);
    justify-content: flex-end;
}

.output-tabs {
    display: flex;
    border-bottom: 1px solid var(--border-color);
    margin-bottom: var(--spacing-lg);
}

.workflow-visualization {
    min-height: 500px;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    overflow: hidden;
}

.n8n-demo-container {
    width: 100%;
    height: 500px;
    background: var(--bg-tertiary);
    display: flex;
    align-items: center;
    justify-content: center;
}

.json-container {
    height: 500px;
    display: flex;
    flex-direction: column;
}

.json-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-md);
    padding-bottom: var(--spacing-sm);
    border-bottom: 1px solid var(--border-color);
}

.json-output {
    flex: 1;
    background: var(--bg-dark);
    color: #e2e8f0;
    padding: var(--spacing-md);
    border-radius: var(--radius-md);
    font-family: var(--font-mono);
    font-size: 0.875rem;
    line-height: 1.5;
    overflow: auto;
    white-space: pre-wrap;
    border: 1px solid var(--border-color);
}

.export-options {
    padding: var(--spacing-lg);
    text-align: center;
}

.export-buttons {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
    max-width: 300px;
    margin: 0 auto;
}
