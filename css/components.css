/* Components - Buttons, Cards, Forms */

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-lg);
    border: none;
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: all var(--transition-fast);
    white-space: nowrap;
}

.btn-primary {
    background: var(--primary-color);
    color: var(--text-inverse);
}

.btn-primary:hover {
    background: var(--primary-hover);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-secondary {
    background: var(--bg-tertiary);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
}

.btn-secondary:hover {
    background: var(--bg-primary);
    border-color: var(--border-hover);
}

.btn-large {
    padding: var(--spacing-md) var(--spacing-xl);
    font-size: 1rem;
}

.btn-small {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: 0.75rem;
}

/* Cards */
.feature-card {
    background: var(--bg-card);
    padding: var(--spacing-xl);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    text-align: center;
    transition: all var(--transition-normal);
    border: 1px solid var(--border-color);
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

.feature-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--spacing-lg);
}

.feature-icon i {
    font-size: 1.5rem;
    color: white;
}

/* Forms */
textarea, input {
    width: 100%;
    padding: var(--spacing-md);
    border: 2px solid var(--border-color);
    border-radius: var(--radius-md);
    font-family: inherit;
    font-size: 1rem;
    line-height: 1.5;
    transition: border-color var(--transition-fast);
    background: var(--bg-primary);
    color: var(--text-primary);
}

textarea:focus, input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

textarea {
    min-height: 120px;
    resize: vertical;
}

/* Template Buttons */
.template-button {
    background: var(--bg-tertiary);
    color: var(--text-secondary);
    border: 1px solid var(--border-color);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    text-align: left;
    cursor: pointer;
    transition: all var(--transition-fast);
    line-height: 1.4;
}

.template-button:hover {
    background: var(--primary-color);
    color: var(--text-inverse);
    border-color: var(--primary-color);
    transform: translateY(-1px);
}

/* Tabs */
.tab-btn {
    background: none;
    border: none;
    padding: var(--spacing-md) var(--spacing-lg);
    color: var(--text-secondary);
    cursor: pointer;
    border-bottom: 2px solid transparent;
    transition: all var(--transition-fast);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.tab-btn.active {
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
}

.tab-btn:hover {
    color: var(--primary-color);
    background: var(--bg-tertiary);
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* Theme Toggle */
.theme-toggle {
    background: none;
    border: none;
    color: var(--text-secondary);
    font-size: 1.25rem;
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: var(--radius-md);
    transition: all var(--transition-fast);
}

.theme-toggle:hover {
    background: var(--bg-tertiary);
    color: var(--primary-color);
}

/* Usage Indicator */
.usage-indicator {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: var(--spacing-xs);
}

.usage-text {
    font-size: 0.75rem;
    color: var(--text-muted);
}

.usage-bar {
    width: 100px;
    height: 4px;
    background: var(--bg-tertiary);
    border-radius: 2px;
    overflow: hidden;
}

.usage-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--secondary-color), var(--primary-color));
    transition: width var(--transition-normal);
}

/* Placeholder Content */
.placeholder-content {
    text-align: center;
    color: var(--text-muted);
}

.placeholder-content i {
    font-size: 3rem;
    margin-bottom: var(--spacing-md);
    opacity: 0.5;
}

.placeholder-content h3 {
    margin-bottom: var(--spacing-sm);
    color: var(--text-secondary);
}
