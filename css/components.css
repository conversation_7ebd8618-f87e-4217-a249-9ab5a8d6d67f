/* Components - Buttons, Cards, Forms */

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-lg);
    border: none;
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: all var(--transition-fast);
    white-space: nowrap;
}

.btn-primary {
    background: var(--primary-color);
    color: var(--text-inverse);
}

.btn-primary:hover {
    background: var(--primary-hover);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-secondary {
    background: var(--bg-tertiary);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
}

.btn-secondary:hover {
    background: var(--bg-primary);
    border-color: var(--border-hover);
}

.btn-large {
    padding: var(--spacing-md) var(--spacing-xl);
    font-size: 1rem;
}

.btn-small {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: 0.75rem;
}

/* Cards */
.feature-card {
    background: var(--bg-card);
    padding: var(--spacing-xl);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    text-align: center;
    transition: all var(--transition-normal);
    border: 1px solid var(--border-color);
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

.feature-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--spacing-lg);
}

.feature-icon i {
    font-size: 1.5rem;
    color: white;
}

/* Forms */
textarea, input {
    width: 100%;
    padding: var(--spacing-md);
    border: 2px solid var(--border-color);
    border-radius: var(--radius-md);
    font-family: inherit;
    font-size: 1rem;
    line-height: 1.5;
    transition: border-color var(--transition-fast);
    background: var(--bg-primary);
    color: var(--text-primary);
}

textarea:focus, input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

textarea {
    min-height: 120px;
    resize: vertical;
}

/* Template Buttons */
.template-button {
    background: var(--bg-tertiary);
    color: var(--text-secondary);
    border: 1px solid var(--border-color);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    text-align: left;
    cursor: pointer;
    transition: all var(--transition-fast);
    line-height: 1.4;
}

.template-button:hover {
    background: var(--primary-color);
    color: var(--text-inverse);
    border-color: var(--primary-color);
    transform: translateY(-1px);
}

/* Tabs */
.tab-btn {
    background: none;
    border: none;
    padding: var(--spacing-md) var(--spacing-lg);
    color: var(--text-secondary);
    cursor: pointer;
    border-bottom: 2px solid transparent;
    transition: all var(--transition-fast);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.tab-btn.active {
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
}

.tab-btn:hover {
    color: var(--primary-color);
    background: var(--bg-tertiary);
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* Theme Toggle */
.theme-toggle {
    background: none;
    border: none;
    color: var(--text-secondary);
    font-size: 1.25rem;
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: var(--radius-md);
    transition: all var(--transition-fast);
}

.theme-toggle:hover {
    background: var(--bg-tertiary);
    color: var(--primary-color);
}

/* Usage Indicator */
.usage-indicator {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: var(--spacing-xs);
}

.usage-text {
    font-size: 0.75rem;
    color: var(--text-muted);
}

.usage-bar {
    width: 100px;
    height: 4px;
    background: var(--bg-tertiary);
    border-radius: 2px;
    overflow: hidden;
}

.usage-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--secondary-color), var(--primary-color));
    transition: width var(--transition-normal);
}

/* Placeholder Content */
.placeholder-content {
    text-align: center;
    color: var(--text-muted);
}

.placeholder-content i {
    font-size: 3rem;
    margin-bottom: var(--spacing-md);
    opacity: 0.5;
}

.placeholder-content h3 {
    margin-bottom: var(--spacing-sm);
    color: var(--text-secondary);
}

/* Pricing Cards */
.pricing {
    padding: var(--spacing-2xl) 0;
    background: var(--bg-secondary);
}

.pricing-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-xl);
    margin-top: var(--spacing-xl);
}

.pricing-card {
    background: var(--bg-card);
    border-radius: var(--radius-lg);
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-md);
    border: 2px solid var(--border-color);
    position: relative;
    transition: all var(--transition-normal);
}

.pricing-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

.pricing-card.featured {
    border-color: var(--primary-color);
    transform: scale(1.05);
}

.pricing-badge {
    position: absolute;
    top: -12px;
    left: 50%;
    transform: translateX(-50%);
    background: var(--primary-color);
    color: var(--text-inverse);
    padding: var(--spacing-xs) var(--spacing-md);
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    font-weight: 500;
}

.pricing-header {
    text-align: center;
    margin-bottom: var(--spacing-xl);
}

.pricing-header h3 {
    font-size: 1.5rem;
    margin-bottom: var(--spacing-md);
    color: var(--text-primary);
}

.price {
    display: flex;
    align-items: baseline;
    justify-content: center;
    gap: var(--spacing-xs);
}

.currency {
    font-size: 1.25rem;
    color: var(--text-secondary);
}

.amount {
    font-size: 3rem;
    font-weight: 700;
    color: var(--primary-color);
}

.period {
    font-size: 1rem;
    color: var(--text-secondary);
}

.pricing-features {
    list-style: none;
    margin-bottom: var(--spacing-xl);
}

.pricing-features li {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) 0;
    color: var(--text-secondary);
}

.pricing-features i {
    color: var(--secondary-color);
    font-size: 0.875rem;
}

/* Advanced Template System */
.template-search {
    position: relative;
    margin-bottom: var(--spacing-lg);
}

.template-search-input {
    width: 100%;
    padding: var(--spacing-md) var(--spacing-xl) var(--spacing-md) var(--spacing-md);
    border: 2px solid var(--border-color);
    border-radius: var(--radius-md);
    font-size: 1rem;
    background: var(--bg-primary);
    color: var(--text-primary);
}

.template-search i {
    position: absolute;
    right: var(--spacing-md);
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-muted);
}

.template-categories {
    display: flex;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-lg);
    overflow-x: auto;
    padding-bottom: var(--spacing-sm);
}

.category-tab {
    background: var(--bg-tertiary);
    color: var(--text-secondary);
    border: 1px solid var(--border-color);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    cursor: pointer;
    transition: all var(--transition-fast);
    white-space: nowrap;
    flex-shrink: 0;
}

.category-tab.active {
    background: var(--primary-color);
    color: var(--text-inverse);
    border-color: var(--primary-color);
}

.category-tab:hover {
    background: var(--primary-hover);
    color: var(--text-inverse);
    border-color: var(--primary-hover);
}

.templates-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: var(--spacing-lg);
    max-height: 600px;
    overflow-y: auto;
    padding: var(--spacing-sm);
}

.template-card {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    transition: all var(--transition-normal);
    cursor: pointer;
}

.template-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-color);
}

.template-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--spacing-md);
}

.template-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
    line-height: 1.3;
}

.template-category {
    background: var(--bg-tertiary);
    color: var(--text-muted);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
    font-weight: 500;
    white-space: nowrap;
}

.template-description {
    color: var(--text-secondary);
    font-size: 0.875rem;
    line-height: 1.5;
    margin-bottom: var(--spacing-lg);
}

.template-actions {
    display: flex;
    gap: var(--spacing-sm);
    justify-content: flex-end;
}

.no-results {
    grid-column: 1 / -1;
    text-align: center;
    color: var(--text-muted);
    padding: var(--spacing-2xl);
    font-style: italic;
}

/* Template Modal */
.template-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    padding: var(--spacing-lg);
}

.modal-content {
    background: var(--bg-card);
    border-radius: var(--radius-lg);
    max-width: 800px;
    width: 100%;
    max-height: 90vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    box-shadow: var(--shadow-xl);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
}

.modal-header h3 {
    margin: 0;
    color: var(--text-primary);
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--text-muted);
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: var(--radius-sm);
    transition: all var(--transition-fast);
}

.modal-close:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

.modal-body {
    padding: var(--spacing-lg);
    overflow-y: auto;
    flex: 1;
}

.template-meta {
    margin-bottom: var(--spacing-lg);
}

.template-category-badge {
    background: var(--primary-color);
    color: var(--text-inverse);
    padding: var(--spacing-xs) var(--spacing-md);
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    font-weight: 500;
}

.template-full-description {
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: var(--spacing-lg);
}

.template-prompt-preview h4 {
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
}

.template-prompt-preview pre {
    background: var(--bg-dark);
    color: #e2e8f0;
    padding: var(--spacing-lg);
    border-radius: var(--radius-md);
    font-family: var(--font-mono);
    font-size: 0.875rem;
    line-height: 1.5;
    overflow-x: auto;
    white-space: pre-wrap;
    border: 1px solid var(--border-color);
}

.modal-footer {
    display: flex;
    gap: var(--spacing-md);
    justify-content: flex-end;
    padding: var(--spacing-lg);
    border-top: 1px solid var(--border-color);
}

/* Deployment Options */
.deploy-options {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
    margin-top: var(--spacing-lg);
}

.deploy-option {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-lg);
    text-align: left;
    border: 2px solid var(--border-color);
    border-radius: var(--radius-lg);
    background: var(--bg-card);
    transition: all var(--transition-normal);
}

.deploy-option:hover {
    border-color: var(--primary-color);
    background: var(--bg-tertiary);
    transform: translateY(-2px);
}

.deploy-option i {
    font-size: 1.25rem;
    width: 24px;
    text-align: center;
}
