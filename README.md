# GEN8N - AI-Powered n8n Workflow Generator

A beautiful, modern SaaS application that generates n8n automation workflows using AI. Transform your automation ideas into production-ready workflows in seconds.

## ✨ Features

- **AI-Powered Generation**: Advanced AI understands your requirements and generates optimized workflows
- **200+ Integrations**: Connect with all your favorite tools and services
- **Instant Deployment**: Deploy workflows to n8n with a single click
- **Modern UI**: Beautiful, responsive design with dark/light theme support
- **Real-time Progress**: Live progress tracking during workflow generation
- **Usage Analytics**: Track your daily generation usage
- **Template Library**: Quick-start templates for common automation scenarios

## 🏗️ Architecture

### Modular Structure
The application follows a modular architecture with files under 300 lines each for better maintainability:

```
GEN8N/
├── index.html              # Main HTML structure
├── style.css              # CSS imports (main entry)
├── script.js              # JavaScript entry point
├── css/                   # Modular CSS files
│   ├── variables.css      # Design system variables
│   ├── base.css          # Reset and typography
│   ├── components.css    # UI components
│   ├── layout.css        # Layout and sections
│   └── responsive.css    # Responsive design
├── js/                    # Modular JavaScript files
│   ├── config.js         # Configuration and constants
│   ├── api.js           # OpenAI API service
│   ├── ui.js            # UI controller
│   └── app.js           # Main application orchestrator
└── server.py             # Development server
```

### Key Components

#### CSS Modules
- **variables.css**: Design system with CSS custom properties
- **base.css**: Reset styles, typography, and utilities
- **components.css**: Reusable UI components (buttons, cards, forms)
- **layout.css**: Page layout and section styles
- **responsive.css**: Mobile-first responsive design

#### JavaScript Modules
- **config.js**: API keys, settings, and templates
- **api.js**: OpenAI Assistant API integration
- **ui.js**: DOM manipulation and user interface
- **app.js**: Application orchestration and event handling

## 🚀 Getting Started

### Prerequisites
- Modern web browser with ES6 module support
- Python 3.x (for development server)
- OpenAI API key

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd GEN8N
   ```

2. **Configure API Key**
   Update the API key in `js/config.js`:
   ```javascript
   export const CONFIG = {
       OPENAI_API_KEY: 'your-api-key-here',
       // ...
   };
   ```

3. **Start the development server**
   ```bash
   python server.py
   ```

4. **Open in browser**
   Navigate to `http://localhost:8000`

## 🎨 Design System

### Colors
- **Primary**: `#6366f1` (Indigo)
- **Secondary**: `#10b981` (Emerald)
- **Accent**: `#f59e0b` (Amber)
- **Backgrounds**: Light/Dark theme support

### Typography
- **Font**: Inter (Google Fonts)
- **Monospace**: Monaco, Menlo, Ubuntu Mono

### Spacing
- **XS**: 0.25rem
- **SM**: 0.5rem
- **MD**: 1rem
- **LG**: 1.5rem
- **XL**: 2rem
- **2XL**: 3rem

## 🔧 Configuration

### API Settings
```javascript
// js/config.js
export const CONFIG = {
    OPENAI_API_KEY: 'your-key',
    OPENAI_API_BASE_URL: 'https://api.openai.com/v1',
    ASSISTANT_ID: 'your-assistant-id',
    MAX_DAILY_GENERATIONS: 10,
    POLLING_INTERVAL: 1000
};
```

### Templates
Add new workflow templates in `js/config.js`:
```javascript
export const TEMPLATES = [
    "Your new template description...",
    // ...existing templates
];
```

## 📱 Browser Support

- **Modern Browsers**: Chrome 80+, Firefox 72+, Safari 13+, Edge 80+
- **Fallback Mode**: Basic functionality for older browsers
- **Mobile**: Responsive design for all screen sizes

## 🔒 Security

- API keys are stored in configuration files (not in production)
- CORS headers configured for development
- Input validation and error handling
- Rate limiting through usage tracking

## 🚀 Deployment

### Production Checklist
1. **Environment Variables**: Move API keys to environment variables
2. **Build Process**: Minify CSS and JavaScript
3. **CDN**: Serve static assets from CDN
4. **HTTPS**: Enable SSL/TLS
5. **Analytics**: Add tracking (Google Analytics, etc.)

### Hosting Options
- **Static Hosting**: Netlify, Vercel, GitHub Pages
- **Full Stack**: Heroku, AWS, DigitalOcean
- **CDN**: Cloudflare, AWS CloudFront

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Keep files under 300 lines
4. Follow the modular architecture
5. Test across browsers
6. Submit a pull request

## 📄 License

MIT License - see LICENSE file for details

## 🆘 Support

- **Documentation**: Check the code comments
- **Issues**: GitHub Issues
- **Community**: Discord/Slack (if available)

---

Built with ❤️ for the n8n community
