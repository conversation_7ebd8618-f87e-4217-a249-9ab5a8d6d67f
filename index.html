<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GEN8N - AI-Powered n8n Workflow Generator</title>
    <meta name="description" content="Generate powerful n8n automation workflows using AI. Transform your ideas into production-ready automations in seconds.">

    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <!-- Styles -->
    <link rel="stylesheet" href="style.css">

    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🤖</text></svg>">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-brand">
                <i class="fas fa-robot"></i>
                <span>GEN8N</span>
            </div>
            <div class="nav-menu">
                <a href="#features" class="nav-link">Features</a>
                <a href="#pricing" class="nav-link">Pricing</a>
                <a href="#docs" class="nav-link">Docs</a>
                <button class="theme-toggle" id="themeToggle">
                    <i class="fas fa-moon"></i>
                </button>
                <button class="btn btn-primary">Get Started</button>
            </div>
            <div class="nav-toggle" id="navToggle">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero">
        <div class="hero-container">
            <div class="hero-content">
                <h1 class="hero-title">
                    Generate <span class="gradient-text">n8n Workflows</span> with AI
                </h1>
                <p class="hero-subtitle">
                    Transform your automation ideas into production-ready n8n workflows in seconds.
                    No coding required, just describe what you want to automate.
                </p>
                <div class="hero-cta">
                    <button class="btn btn-primary btn-large" onclick="scrollToGenerator()">
                        <i class="fas fa-magic"></i>
                        Start Generating
                    </button>
                    <button class="btn btn-secondary btn-large">
                        <i class="fas fa-play"></i>
                        Watch Demo
                    </button>
                </div>
                <div class="hero-stats">
                    <div class="stat">
                        <span class="stat-number">10K+</span>
                        <span class="stat-label">Workflows Generated</span>
                    </div>
                    <div class="stat">
                        <span class="stat-number">500+</span>
                        <span class="stat-label">Happy Users</span>
                    </div>
                    <div class="stat">
                        <span class="stat-number">99.9%</span>
                        <span class="stat-label">Uptime</span>
                    </div>
                </div>
            </div>
            <div class="hero-visual">
                <div class="workflow-preview">
                    <div class="node node-trigger">
                        <i class="fas fa-play"></i>
                        <span>Trigger</span>
                    </div>
                    <div class="connection"></div>
                    <div class="node node-process">
                        <i class="fas fa-cogs"></i>
                        <span>Process</span>
                    </div>
                    <div class="connection"></div>
                    <div class="node node-action">
                        <i class="fas fa-paper-plane"></i>
                        <span>Action</span>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section id="features" class="features">
        <div class="container">
            <div class="section-header">
                <h2>Powerful Features</h2>
                <p>Everything you need to create amazing automation workflows</p>
            </div>
            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-brain"></i>
                    </div>
                    <h3>AI-Powered Generation</h3>
                    <p>Advanced AI understands your requirements and generates optimized workflows automatically.</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-puzzle-piece"></i>
                    </div>
                    <h3>200+ Integrations</h3>
                    <p>Connect with all your favorite tools and services seamlessly.</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-rocket"></i>
                    </div>
                    <h3>Instant Deployment</h3>
                    <p>Deploy your workflows to n8n with a single click.</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <h3>Enterprise Security</h3>
                    <p>Bank-grade security with SOC 2 compliance and encryption.</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <h3>Analytics & Monitoring</h3>
                    <p>Track performance and optimize your workflows with detailed insights.</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <h3>Team Collaboration</h3>
                    <p>Share and collaborate on workflows with your team members.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Workflow Generator Section -->
    <section id="generator" class="generator">
        <div class="container">
            <div class="section-header">
                <h2>Workflow Generator</h2>
                <p>Describe your automation needs and watch the magic happen</p>
            </div>

            <div class="generator-container">
                <!-- Input Section -->
                <div class="generator-input">
                    <div class="input-header">
                        <h3>Describe Your Workflow</h3>
                        <div class="usage-indicator">
                            <span class="usage-text">3/10 generations used today</span>
                            <div class="usage-bar">
                                <div class="usage-fill" style="width: 30%"></div>
                            </div>
                        </div>
                    </div>

                    <div class="prompt-templates">
                        <h4>
                            <i class="fas fa-magic"></i>
                            AI-Powered Workflow Templates
                        </h4>
                        <p class="templates-subtitle">Choose from enterprise-grade automation scenarios including AI agents, complex business processes, and industry-specific solutions</p>
                        <div class="template-actions-bar">
                            <button class="btn btn-secondary btn-small" id="randomTemplateBtn">
                                <i class="fas fa-random"></i>
                                Random Template
                            </button>
                            <span class="template-count" id="templateCount">Loading templates...</span>
                        </div>
                        <div class="templates-grid" id="promptTemplates">
                            <!-- Advanced templates will be populated by JavaScript -->
                        </div>
                    </div>

                    <div class="prompt-input-container">
                        <textarea
                            id="promptInput"
                            placeholder="Describe your automation workflow... For example: 'Create a workflow that monitors Gmail for new emails and saves attachments to Google Drive'"
                            rows="4"
                        ></textarea>
                        <div class="input-actions">
                            <button class="btn btn-secondary" id="clearBtn">
                                <i class="fas fa-trash"></i>
                                Clear
                            </button>
                            <button class="btn btn-primary" id="generateBtn">
                                <i class="fas fa-magic"></i>
                                Generate Workflow
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Output Section -->
                <div class="generator-output">
                    <div class="output-tabs">
                        <button class="tab-btn active" data-tab="visual">
                            <i class="fas fa-eye"></i>
                            Visual
                        </button>
                        <button class="tab-btn" data-tab="json">
                            <i class="fas fa-code"></i>
                            JSON
                        </button>
                        <button class="tab-btn" data-tab="export">
                            <i class="fas fa-download"></i>
                            Export
                        </button>
                    </div>

                    <div class="tab-content active" id="visual-tab">
                        <div class="workflow-visualization">
                            <div id="n8n-demo-component-display" class="n8n-demo-container">
                                <div class="placeholder-content">
                                    <i class="fas fa-magic"></i>
                                    <h3>Your workflow will appear here</h3>
                                    <p>Generate a workflow to see the visual representation</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="tab-content" id="json-tab">
                        <div class="json-container">
                            <div class="json-header">
                                <span>Generated JSON</span>
                                <button class="btn btn-small" id="copyJsonBtn">
                                    <i class="fas fa-copy"></i>
                                    Copy
                                </button>
                            </div>
                            <pre id="jsonOutput" class="json-output">
                                <div class="placeholder-content">
                                    <i class="fas fa-code"></i>
                                    <p>JSON output will appear here after generation</p>
                                </div>
                            </pre>
                        </div>
                    </div>

                    <div class="tab-content" id="export-tab">
                        <div class="export-options">
                            <h3>Export Options</h3>
                            <div class="export-buttons">
                                <button class="btn btn-primary">
                                    <i class="fas fa-download"></i>
                                    Download JSON
                                </button>
                                <button class="btn btn-secondary">
                                    <i class="fas fa-external-link-alt"></i>
                                    Deploy to n8n
                                </button>
                                <button class="btn btn-secondary">
                                    <i class="fas fa-share"></i>
                                    Share Workflow
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-content">
            <div class="loading-spinner"></div>
            <h3>Generating Your Workflow</h3>
            <p id="loadingText">Analyzing your requirements...</p>
            <div class="loading-progress">
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
                <span class="progress-text" id="progressText">0%</span>
            </div>
        </div>
    </div>

    <!-- Pricing Section -->
    <section id="pricing" class="pricing">
        <div class="container">
            <div class="section-header">
                <h2>Simple, Transparent Pricing</h2>
                <p>Choose the plan that fits your automation needs</p>
            </div>
            <div class="pricing-grid">
                <div class="pricing-card">
                    <div class="pricing-header">
                        <h3>Starter</h3>
                        <div class="price">
                            <span class="currency">$</span>
                            <span class="amount">0</span>
                            <span class="period">/month</span>
                        </div>
                    </div>
                    <ul class="pricing-features">
                        <li><i class="fas fa-check"></i> 10 workflows per day</li>
                        <li><i class="fas fa-check"></i> Basic templates</li>
                        <li><i class="fas fa-check"></i> Community support</li>
                        <li><i class="fas fa-check"></i> JSON export</li>
                    </ul>
                    <button class="btn btn-secondary btn-large">Get Started</button>
                </div>

                <div class="pricing-card featured">
                    <div class="pricing-badge">Most Popular</div>
                    <div class="pricing-header">
                        <h3>Pro</h3>
                        <div class="price">
                            <span class="currency">$</span>
                            <span class="amount">29</span>
                            <span class="period">/month</span>
                        </div>
                    </div>
                    <ul class="pricing-features">
                        <li><i class="fas fa-check"></i> Unlimited workflows</li>
                        <li><i class="fas fa-check"></i> Advanced templates</li>
                        <li><i class="fas fa-check"></i> Priority support</li>
                        <li><i class="fas fa-check"></i> Direct n8n deployment</li>
                        <li><i class="fas fa-check"></i> Team collaboration</li>
                        <li><i class="fas fa-check"></i> Analytics dashboard</li>
                    </ul>
                    <button class="btn btn-primary btn-large">Start Free Trial</button>
                </div>

                <div class="pricing-card">
                    <div class="pricing-header">
                        <h3>Enterprise</h3>
                        <div class="price">
                            <span class="currency">$</span>
                            <span class="amount">99</span>
                            <span class="period">/month</span>
                        </div>
                    </div>
                    <ul class="pricing-features">
                        <li><i class="fas fa-check"></i> Everything in Pro</li>
                        <li><i class="fas fa-check"></i> Custom integrations</li>
                        <li><i class="fas fa-check"></i> Dedicated support</li>
                        <li><i class="fas fa-check"></i> SLA guarantee</li>
                        <li><i class="fas fa-check"></i> On-premise deployment</li>
                        <li><i class="fas fa-check"></i> Custom training</li>
                    </ul>
                    <button class="btn btn-secondary btn-large">Contact Sales</button>
                </div>
            </div>
        </div>
    </section>

    <!-- Scripts -->
    <script src="https://unpkg.com/@n8n/chat@latest/dist/chat.bundle.js"></script>
    <script type="module" src="script.js"></script>
</body>
</html>
