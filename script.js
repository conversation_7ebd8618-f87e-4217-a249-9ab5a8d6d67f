// Modern GEN8N - Modular Architecture
// Import the main application module
import('./js/app.js').then(module => {
    console.log('🚀 GEN8N App loaded successfully');
}).catch(error => {
    console.error('❌ Failed to load GEN8N App:', error);

    // Fallback initialization for older browsers
    initializeFallback();
});

// Fallback for browsers that don't support ES modules
function initializeFallback() {
    console.log('🔄 Initializing fallback mode...');

    // Basic functionality without modules
    document.addEventListener('DOMContentLoaded', () => {
        initializeBasicUI();
    });
}

function initializeBasicUI() {
    // Basic theme toggle
    const themeToggle = document.getElementById('themeToggle');
    if (themeToggle) {
        themeToggle.addEventListener('click', () => {
            const currentTheme = document.documentElement.getAttribute('data-theme') || 'light';
            const newTheme = currentTheme === 'light' ? 'dark' : 'light';
            document.documentElement.setAttribute('data-theme', newTheme);
            localStorage.setItem('gen8n-theme', newTheme);
        });
    }

    // Basic mobile navigation
    const navToggle = document.getElementById('navToggle');
    const navMenu = document.querySelector('.nav-menu');
    if (navToggle && navMenu) {
        navToggle.addEventListener('click', () => {
            navMenu.classList.toggle('active');
        });
    }

    // Basic template functionality
    initializeTemplates();

    // Basic generation (simplified)
    const generateBtn = document.getElementById('generateBtn');
    if (generateBtn) {
        generateBtn.addEventListener('click', handleBasicGeneration);
    }
}

function initializeTemplates() {
    const templates = [
        // AI Agent Examples
        "Create an AI customer support agent that monitors Zendesk tickets, analyzes sentiment, auto-responds to simple queries, and escalates complex issues",
        "Build an AI content creation pipeline that monitors trending topics, generates blog posts using GPT-4, and schedules social media publication",
        "Design an AI sales assistant that enriches leads, scores them with AI, personalizes outreach messages, and updates CRM automatically",

        // Complex Business Automation
        "Build a comprehensive e-commerce fulfillment system that processes Shopify orders, validates inventory, calculates shipping, and updates QuickBooks",
        "Create a multi-tenant SaaS onboarding workflow that provisions AWS resources, sends welcome sequences, and tracks feature adoption",
        "Design a lead nurturing system that scores leads with AI, segments audiences, triggers email sequences, and optimizes campaigns",

        // Advanced Data Processing
        "Create a real-time IoT data pipeline with ML anomaly detection, predictive maintenance scheduling, and executive dashboards",
        "Build a social media intelligence system that monitors brand mentions, analyzes sentiment, identifies influencers, and tracks ROI",
        "Design a fraud detection workflow with real-time ML scoring, blacklist checking, and automated transaction blocking",

        // Industry-Specific
        "Create a healthcare patient management system with appointment scheduling, insurance processing, and HIPAA-compliant reporting",
        "Build a real estate lead system that captures leads from multiple platforms, qualifies with AI, and manages property viewings"
    ];

    const promptTemplates = document.getElementById('promptTemplates');
    const promptInput = document.getElementById('promptInput');

    if (promptTemplates && promptInput) {
        templates.forEach(template => {
            const button = document.createElement('button');
            button.className = 'template-button';
            button.textContent = template;
            button.addEventListener('click', () => {
                promptInput.value = template;
            });
            promptTemplates.appendChild(button);
        });
    }
}

async function handleBasicGeneration() {
    const promptInput = document.getElementById('promptInput');
    const generateBtn = document.getElementById('generateBtn');
    const jsonOutput = document.getElementById('jsonOutput');

    if (!promptInput || !generateBtn || !jsonOutput) return;

    const prompt = promptInput.value.trim();
    if (!prompt) {
        alert('Please enter a workflow description');
        return;
    }

    try {
        generateBtn.disabled = true;
        generateBtn.textContent = 'Generating...';

        // Use the original generation function
        const workflowJson = await generateWorkflowBasic(prompt);
        jsonOutput.textContent = JSON.stringify(workflowJson, null, 2);

    } catch (error) {
        jsonOutput.textContent = `Error: ${error.message}`;
    } finally {
        generateBtn.disabled = false;
        generateBtn.innerHTML = '<i class="fas fa-magic"></i> Generate Workflow';
    }
}

// Simplified version of the original function for fallback
async function generateWorkflowBasic(prompt) {
    const OPENAI_API_KEY = '********************************************************************************************************************************************************************';
    const OPENAI_API_BASE_URL = 'https://api.openai.com/v1';
    const ASSISTANT_ID = 'asst_EVjHFlXIaDM1E3xHOqzJxsXs';

    // Create thread
    const threadResponse = await fetch(`${OPENAI_API_BASE_URL}/threads`, {
        method: "POST",
        headers: {
            "Authorization": `Bearer ${OPENAI_API_KEY}`,
            "OpenAI-Beta": "assistants=v2",
            "Content-Type": "application/json"
        },
        body: JSON.stringify({})
    });

    if (!threadResponse.ok) throw new Error('Failed to create thread');
    const threadData = await threadResponse.json();

    // Add message
    await fetch(`${OPENAI_API_BASE_URL}/threads/${threadData.id}/messages`, {
        method: "POST",
        headers: {
            "Authorization": `Bearer ${OPENAI_API_KEY}`,
            "OpenAI-Beta": "assistants=v2",
            "Content-Type": "application/json"
        },
        body: JSON.stringify({ role: "user", content: prompt })
    });

    // Create run
    const runResponse = await fetch(`${OPENAI_API_BASE_URL}/threads/${threadData.id}/runs`, {
        method: "POST",
        headers: {
            "Authorization": `Bearer ${OPENAI_API_KEY}`,
            "OpenAI-Beta": "assistants=v2",
            "Content-Type": "application/json"
        },
        body: JSON.stringify({ assistant_id: ASSISTANT_ID })
    });

    const runData = await runResponse.json();

    // Poll for completion (simplified)
    let attempts = 0;
    while (attempts < 30) {
        await new Promise(resolve => setTimeout(resolve, 2000));

        const statusResponse = await fetch(`${OPENAI_API_BASE_URL}/threads/${threadData.id}/runs/${runData.id}`, {
            headers: {
                "Authorization": `Bearer ${OPENAI_API_KEY}`,
                "OpenAI-Beta": "assistants=v2"
            }
        });

        const statusData = await statusResponse.json();
        if (statusData.status === 'completed') break;
        if (statusData.status === 'failed') throw new Error('Generation failed');
        attempts++;
    }

    // Get messages
    const messagesResponse = await fetch(`${OPENAI_API_BASE_URL}/threads/${threadData.id}/messages`, {
        headers: {
            "Authorization": `Bearer ${OPENAI_API_KEY}`,
            "OpenAI-Beta": "assistants=v2"
        }
    });

    const messagesData = await messagesResponse.json();
    const assistantMessage = messagesData.data.find(msg => msg.role === 'assistant');

    if (!assistantMessage) throw new Error('No response from assistant');

    const textContent = assistantMessage.content.find(content => content.type === 'text');
    const jsonMatch = textContent.text.value.match(/```json\n([\s\S]*?)\n```/);

    if (jsonMatch && jsonMatch[1]) {
        return JSON.parse(jsonMatch[1]);
    }

    throw new Error('Could not extract JSON from response');
}

// Legacy code removed - now handled by modular architecture
